package com.fansunited.automation.leaguesapi.membership.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.core.apis.leagueapi.leagues.CreateLeagueEndpoint.createLeague;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.validators.LeaguesApiValidator.getUsersIds;
import static org.hamcrest.Matchers.hasItem;

import com.fansunited.automation.core.base.leaguesapi.LeagueBaseTest;
import com.fansunited.automation.model.leaguesapi.response.LeagueData;
import com.google.firebase.auth.FirebaseAuthException;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("League - Membership Api - POST /v1/membership/{leagueId} endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class InviteMembershipTest extends LeagueBaseTest {

  @DisplayName("Ensure that league admin can  invite users to the league.")
  @Test
  @Tag(REGRESSION)
  public void adminCanInviteUsers()
      throws IOException, ExecutionException, FirebaseAuthException, InterruptedException, HttpException {
    var request = leagueRequest;
    var email = getCurrentTestUser().getEmail();
    var league = createLeague(request, null, email).as(LeagueData.class);
    String leagueId = league.getLeague().getId();

    List<String> membersIds = getUsersIds(createUsers(generateRandomNumber(2, 5)));
    var response = inviteUsers(membersIds, leagueId, email);
    currentTestResponse.set(response);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);
    membersIds.forEach(memberId -> response.then().assertThat().body("data", hasItem(memberId)));
  }
}
