package com.fansunited.automation.loyaltyapi.templates.delete;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.BILLING_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_PRODUCTION_TESTING_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_CLIENT_ID;
import static com.fansunited.automation.core.base.AuthBase.createUser;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProviderUser;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.loyaltyapi.LeaderboardByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplateByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.TemplateBaseTest;
import com.fansunited.automation.helpers.DateTripletHelper;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Loyalty Api - DELETE /v1/leaderboard/templates/{templateId} endpoint validation tests")
public class DeleteTemplateValidationTests extends TemplateBaseTest {

  @ParameterizedTest(name = "Verify API cannot delete template with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArguments.class)
  public void deleteTemplateWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        TemplateByIdEndpoint.deleteLeaderboardTemplate("FgGsa239fGw912dSpT", CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API cannot delete template with invalid JWT token. JWT token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProviderUser.class)
  public void deleteTemplateWithInvalidJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder)
      throws HttpException {

    var response = TemplateByIdEndpoint.deleteLeaderboardTemplate("FgGsa239fGw912dSpT",
        argumentsHolder.getJwtToken(), null, true, null, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API cannot delete template without auth")
  public void deleteTemplateWithoutAuth() throws HttpException {

    var response = TemplateByIdEndpoint.deleteLeaderboardTemplate("FgGsa239fGw912dSpT",
        null, null, false, null, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify users with insufficient permissions cannot delete templates")
  public void deleteTemplateWithInsufficientPermissions() throws HttpException {

    var response =
        TemplateByIdEndpoint.deleteLeaderboardTemplate("FgGsa239fGw912dSpT",
            null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, true, BILLING_USER,
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify client from one project cannot delete templates from another project")
  public void deleteAnotherProjectsTemplate() throws HttpException {

    var response =
        TemplateByIdEndpoint.deleteLeaderboardTemplate("FgGsa239fGw912dSpT",
            CLIENT_PRODUCTION_TESTING_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify end users cannot delete templates")
  public void deleteTemplatesUsingEndUserAccount()
      throws HttpException, IOException, ExecutionException, FirebaseAuthException,
      InterruptedException {

    var user = createUser();

    var response =
        TemplateByIdEndpoint.deleteLeaderboardTemplate("FgGsa239fGw912dSpT", null,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, true, user.getEmail(),
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @ParameterizedTest(name = "Verify template cannot be deleted with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void deleteTemplateWithInvalidClientId(String clientId) throws HttpException {

    var response =
        TemplateByIdEndpoint.deleteLeaderboardTemplate("FgGsa239fGw912dSpT",
            clientId);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponseEmptyBody(response,
        List.of(HttpStatus.SC_FORBIDDEN, HttpStatus.SC_BAD_REQUEST));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns NOT_FOUND when deleting template with invalid template id")
  public void deleteTemplateWithInvalidTemplateId() throws HttpException {

    var response =
        TemplateByIdEndpoint.deleteLeaderboardTemplate("FgGsa239fGw912dSpT");

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_NOT_FOUND);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when deleting template with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void deleteTemplateWithNonSupportedContentType(ContentType contentType)
      throws HttpException {

    var response =
        TemplateByIdEndpoint.deleteLeaderboardTemplate("FgGsa239fGw912dSpT",
            CLIENT_PRODUCTION_TESTING_ID, AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API successfully deletes template if content type is NOT specified")
  public void deleteTemplateWithoutSpecifyingContentType()
      throws HttpException, InterruptedException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(fromDateString)
        .toDate(toDateString)
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var templateResponse = createTemplateResponse.as(TemplateResponse.class);

    TimeUnit.SECONDS.sleep(
        30); // Template cannot be deleted immediately when running templates tests in parallel

    var deleteTemplateResponse =
        TemplateByIdEndpoint.deleteLeaderboardTemplate(templateResponse.getId(),
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(deleteTemplateResponse);

    deleteTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    LeaderboardByIdEndpoint.getLeaderboardForTemplateWithId(templateResponse.getId(), null, null)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_NOT_FOUND);
  }
}
