package com.fansunited.automation.loyaltyapi.templates.delete;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;

import com.fansunited.automation.core.apis.loyaltyapi.LeaderboardByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplateByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.TemplateBaseTest;
import com.fansunited.automation.helpers.DateTripletHelper;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.github.javafaker.Faker;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Loyalty Api - DELETE /v1/leaderboard/templates/{templateId} endpoint happy path tests")
public class DeleteTemplateTests extends TemplateBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify templates deletion")
  public void verifyTemplatesDeletion() throws HttpException, InterruptedException {

    var markets =
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.CORRECT_SCORE.getValue());

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(fromDateString)
        .toDate(toDateString)
        .markets(markets)
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    TimeUnit.SECONDS.sleep(
        60); // Template cannot be deleted immediately when running templates tests in parallel

    var templateResponse = createTemplateResponse.as(TemplateResponse.class);

    var deleteTemplateResponse =
        TemplateByIdEndpoint.deleteLeaderboardTemplate(templateResponse.getId());

    currentTestResponse.set(deleteTemplateResponse);

    deleteTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    LeaderboardByIdEndpoint.getLeaderboardForTemplateWithId(templateResponse.getId(), null, null)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_NOT_FOUND);
  }
}
