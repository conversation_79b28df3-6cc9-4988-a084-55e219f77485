package com.fansunited.automation.loyaltyapi.statistics;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProviderUser;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.loyaltyapi.StatisticsEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Loyalty Api - GET /v1/statistics endpoint validation tests")
public class GetOwnStatisticsValidationTests extends AuthBase {

  @ParameterizedTest(name = "Verify own statistics cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArguments.class)
  public void getOwnStatisticsWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        StatisticsEndpoint.getOwnStatistics(CLIENT_AUTOMATION_ID, getCurrentTestUser().getEmail(),
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting own statistics with invalid client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getOwnStatisticsWithInvalidClientId(String clientId)
      throws HttpException {

    var response =
        StatisticsEndpoint.getOwnStatistics(clientId, getCurrentTestUser().getEmail());

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify own statistics cannot be fetched with invalid JWT token. Jwt token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProviderUser.class)
  public void getOwnStatisticsWithInvalidJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        StatisticsEndpoint.getOwnStatistics(CLIENT_AUTOMATION_ID, argumentsHolder.getJwtToken(),
            null, true, null, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(anyOf(is(HttpStatus.SC_FORBIDDEN), is(HttpStatus.SC_UNAUTHORIZED)));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify own statistics cannot be fetched without JWT token")
  public void getOwnStatisticsWithoutJwtToken() throws HttpException {

    var response =
        StatisticsEndpoint.getOwnStatistics(CLIENT_AUTOMATION_ID, null,
            null, false, null, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(anyOf(equalTo(HttpStatus.SC_UNAUTHORIZED), equalTo(HttpStatus.SC_FORBIDDEN)));  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting own statistics with non supported content type. Content type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getOwnStatisticsWithNonSupportedContentType(ContentType contentType)
      throws HttpException {

    var response =
        StatisticsEndpoint.getOwnStatistics(CLIENT_AUTOMATION_ID, getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify own statistics are fetched in JSON format if content type is NOT specified")
  public void getOwnStatisticsWithoutSpecifyingContentType() throws HttpException {

    var response =
        StatisticsEndpoint.getOwnStatistics(CLIENT_AUTOMATION_ID, getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
