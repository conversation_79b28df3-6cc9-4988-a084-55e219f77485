package com.fansunited.automation.loyaltyapi.ranks;

import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.GAME_TYPE_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.MESSAGE_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.PROFILE_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.ApiErrorCodes.LoyaltyErrorCodes.CODE_INVALID_ACTION;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.predictionapi.GameEndpoint.updateGame;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.GameLeaderboardsEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.GetRankingByProfileId;
import com.fansunited.automation.core.apis.loyaltyapi.enums.UserRankingTypes;
import com.fansunited.automation.core.apis.predictionapi.CreateGameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.core.watchers.TestWatcherImpl;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import com.fansunited.automation.model.predictionapi.games.request.UpdateGameRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Leaderboards should have unique matches, teams, competitions
 */
@DisplayName("Loyalty Api - GET /v1/leaderboard/{templateId} endpoint happy path tests")
public class GetLeaderboardRankingForSeparateUserTests extends AuthBase {

  private static final Logger LOG = LoggerFactory.getLogger(
      GetLeaderboardRankingForSeparateUserTests.class);

  @BeforeAll
  public static void cleanUp() {
    LOG.debug("Setting shouldTruncateTables to true");
    TestWatcherImpl.setShouldTruncateTables(true);
  }

  @Test
  @Tags({@Tag(REGRESSION)})
  @DisplayName("Get user rankings for custom template with specific competition")
  public void getUserRankingsForTemplateSpecificComp() throws Exception {

    var gameId = GamesEndpoint.createGames(GameType.TOP_X, 1).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

      var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.HT_1X2,
        GameType.TOP_X, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORNERS_MATCH,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.HT_1X2,
        GameType.TOP_X, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.HT_1X2,
        GameType.TOP_X, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    GetRankingByProfileId getRankingByProfileIdBuilder = GetRankingByProfileId.builder()
        .userId(profileIdFirstPlace)
        .gameIds(List.of(gameId))
        .type_ids(null)
        .rankType(UserRankingTypes.GAME)
        .build();

    var getRankingFirstPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingFirstPlaceResponse);

    getRankingFirstPlaceResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0].position", equalTo(1));

    getRankingByProfileIdBuilder.setUserId(profileIdSecondPlace);
    var getRankingSecondPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingSecondPlaceResponse);

    getRankingSecondPlaceResponse
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0].position", equalTo(2));

    getRankingByProfileIdBuilder.setUserId(profileIdThirdPlace);
    var getRankingThirdPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingThirdPlaceResponse);

    getRankingThirdPlaceResponse
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0].position", equalTo(3));
  }

  @ParameterizedTest(name = "Validate game_type = null for rank_type = 'TEMPLATE' when GET ranking by profile id for game = {arguments}")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1544")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.INCLUDE, names = {"TOP_X", "MATCH_QUIZ"})
  public void gameTypeNullWhenRankTypeTemplateForUserRanking(GameType gameType)
      throws Exception {

    //Create a game
    CreateGameEndpoint createGameEndpoint = CreateGameEndpoint.builder()
        .gameType(gameType)
        .build();

    GameInstance gameInstance = createGameEndpoint.createGame().as(GameInstance.class);

    //Mock Data
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = getCurrentTestUser().getUid();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        gameType, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.HT_1X2,
        gameType, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORNERS_MATCH,
        gameType, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    GetRankingByProfileId getRankingByProfileIdBuilder = GetRankingByProfileId.builder()
        .userId(profileIdFirstPlace)
        .gameIds(List.of(gameInstance.getId()))
        .type_ids("test")
        .rankType(UserRankingTypes.TEMPLATE)
        .build();

    var getRankingFirstPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingFirstPlaceResponse);

    getRankingFirstPlaceResponse
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + GAME_TYPE_PROP, everyItem(is(nullValue())));
  }

  @ParameterizedTest(name = "Validate when game_type = not null and rank_type = 'TEMPLATE' when GET ranking by profile id, error is thrown ")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1544")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.INCLUDE, names = {"TOP_X", "MATCH_QUIZ"})
  public void gameTypeNotNullAndRankTypeTemplateForUserRankingReturnsError(GameType gameType)
      throws Exception {

    //Create a game
    CreateGameEndpoint createGameEndpoint = CreateGameEndpoint.builder()
        .gameType(gameType)
        .build();

    GameInstance gameInstance = createGameEndpoint.createGame().as(GameInstance.class);

    //Mock Data
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = getCurrentTestUser().getUid();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        gameType, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.HT_1X2,
        gameType, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORNERS_MATCH,
        gameType, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    GetRankingByProfileId getRankingByProfileIdBuilder = GetRankingByProfileId.builder()
        .userId(profileIdFirstPlace)
        .gameIds(List.of(gameInstance.getId()))
        .rankType(UserRankingTypes.TEMPLATE)
        .gameType(gameType.getValue())
        .build();

    var getRankingFirstPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingFirstPlaceResponse);

    getRankingFirstPlaceResponse
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error." + MESSAGE_PROP,
            equalTo("'rank_type' must be set to 'game' when adding filter 'game_type'."))
        .body("error." + STATUS_PROP, equalTo(CODE_INVALID_ACTION));
  }

  @ParameterizedTest(name = "Validate game_type field is exposed when rank_type = 'GAME' in GET ranking by profile id for game = {arguments}")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1544")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.INCLUDE, names = {"TOP_X", "MATCH_QUIZ"})
  public void gameTypeNotNullWhenRankTypeGameForUserRanking(GameType gameType)
      throws Exception {

    //Create a game
    CreateGameEndpoint createGameEndpoint = CreateGameEndpoint.builder()
        .gameType(gameType)
        .build();

    GameInstance gameInstance = createGameEndpoint.createGame().as(GameInstance.class);

    //Mock Data
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = getCurrentTestUser().getUid();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        gameType, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.HT_1X2,
        gameType, 10,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORNERS_MATCH,
        gameType, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    GetRankingByProfileId getRankingByProfileIdBuilder = GetRankingByProfileId.builder()
        .userId(profileIdFirstPlace)
        .type_ids((gameInstance.getId()))
        .rankType(UserRankingTypes.GAME)
        .build();

    var getRankingFirstPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingFirstPlaceResponse);

    getRankingFirstPlaceResponse
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + GAME_TYPE_PROP, equalTo(List.of(gameType.getValue())));
  }

  @ParameterizedTest(name = "Validate GET ranking by profile id when rank_type = 'GAME' can be filtered by game_type = {arguments}")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1544")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.INCLUDE, names = {"TOP_X", "MATCH_QUIZ"})
  public void userRankingFilteredByGameType(GameType gameTypeFilter)
      throws Exception {

    //Create a TOP X game
    CreateGameEndpoint createGameEndpoint = CreateGameEndpoint.builder()
        .gameType(GameType.TOP_X)
        .build();

    GameInstance gameInstanceTopX = createGameEndpoint.createGame().as(GameInstance.class);

    //Create a MATCH QUIZ game
    createGameEndpoint = CreateGameEndpoint.builder()
        .gameType(GameType.MATCH_QUIZ)
        .build();

    GameInstance gameInstanceMatchQuiz = createGameEndpoint.createGame().as(GameInstance.class);

    List<String> gameInstances = List.of(gameInstanceTopX.getId(), gameInstanceMatchQuiz.getId());

    //Mock Data
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = getCurrentTestUser().getUid();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstanceTopX.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstanceTopX.getFixtures().get(0).getMatchId()), 1,
        "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.HT_1X2,
        GameType.TOP_X, 10,
        gameInstanceTopX.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstanceTopX.getFixtures().get(1).getMatchId()), 1,
        "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORNERS_MATCH,
        GameType.MATCH_QUIZ, 30,
        gameInstanceMatchQuiz.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstanceMatchQuiz.getFixtures().get(1).getMatchId()),
        1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORNERS_MATCH,
        GameType.MATCH_QUIZ, 10,
        gameInstanceMatchQuiz.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstanceMatchQuiz.getFixtures().get(1).getMatchId()),
        1, "1",
        predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstanceTopX.getId());
    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstanceMatchQuiz.getId());

    GetRankingByProfileId getRankingByProfileIdBuilder = GetRankingByProfileId.builder()
        .userId(profileIdFirstPlace)
        .gameIds(gameInstances.stream().toList())
        .rankType(UserRankingTypes.GAME)
        .gameType(gameTypeFilter.getValue())
        .build();

    var getRankingFirstPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingFirstPlaceResponse);

    getRankingFirstPlaceResponse
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + GAME_TYPE_PROP, equalTo(List.of(gameTypeFilter.getValue())));
  }

  @ParameterizedTest(name = "Validate GET ranking by profile id when rank_type = 'GAME' returns empty body when game_type = {arguments} but no rankings")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1544")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.INCLUDE, names = {"TOP_X", "MATCH_QUIZ"})
  public void filterByGameTypeWhenNoSuchTypeReturnsEmptyBodyForUserRankings(GameType gameTypeFilter)
      throws Exception {

    GameInstance gameInstance = new GameInstance();
    CreateGameEndpoint createGameEndpoint;
    PredictionType predictionType;

    if (gameTypeFilter == GameType.MATCH_QUIZ) {
      //Create a TOP X game
      createGameEndpoint = CreateGameEndpoint.builder()
          .gameType(GameType.TOP_X)
          .build();
      gameInstance = createGameEndpoint.createGame().as(GameInstance.class);
    } else {
      //Create a MATCH QUIZ game
      createGameEndpoint = CreateGameEndpoint.builder()
          .gameType(GameType.MATCH_QUIZ)
          .build();
      gameInstance = createGameEndpoint.createGame().as(GameInstance.class);
    }

    //Mock Data
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = getCurrentTestUser().getUid();

    GetRankingByProfileId getRankingByProfileIdBuilder = GetRankingByProfileId.builder()
        .userId(profileIdFirstPlace)
        .gameIds(List.of(gameInstance.getId()))
        .rankType(UserRankingTypes.GAME)
        .gameType(gameTypeFilter.getValue())
        .build();

    var getRankingFirstPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingFirstPlaceResponse);

    getRankingFirstPlaceResponse
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.size()", equalTo(0));
  }

  @Test()
  @Tags({@Tag(REGRESSION), @Tag("FZ-1544")})
  @DisplayName("Validate GET ranking by profile id when game_type = 'SINGLE' returns an error")
  public void gameTypeSingleForUserRankingReturnsError()
      throws HttpException {

    GetRankingByProfileId getRankingByProfileIdBuilder = GetRankingByProfileId.builder()
        .userId(getCurrentTestUser().getUid())
        .rankType(UserRankingTypes.GAME)
        .gameType("SINGLE")
        .build();

    var getRankingFirstPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingFirstPlaceResponse);

    getRankingFirstPlaceResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error." + MESSAGE_PROP,
            equalTo("gameType: Must be MATCH_QUIZ or TOP_X."))
        .body("error." + STATUS_PROP, equalTo(STATUS_VALIDATION_ERROR));
  }

  @ParameterizedTest(name = "Validate GET ranking by profile id when game_type = {arguments} and missing rank_type param returns an error")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1544")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.INCLUDE, names = {"TOP_X", "MATCH_QUIZ"})
  public void gameTypeNotNullAndMissingRankTypeForUserRankingReturnsError(GameType gameType)
      throws HttpException {

    GetRankingByProfileId getRankingByProfileIdBuilder = GetRankingByProfileId.builder()
        .userId(getCurrentTestUser().getUid())
        .gameType(gameType.getValue())
        .build();

    var getRankingFirstPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingFirstPlaceResponse);

    getRankingFirstPlaceResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error." + MESSAGE_PROP,
            equalTo("'rank_type' must be set to 'game' when adding filter 'game_type'."))
        .body("error." + STATUS_PROP, equalTo(CODE_INVALID_ACTION));
  }

  @ParameterizedTest(name = "Validate GET ranking by profile id when game_type = not null and rank_type = null returns an error")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1544")})
  @EnumSource(value = GameType.class, mode = EnumSource.Mode.INCLUDE, names = {"TOP_X", "MATCH_QUIZ"})
  public void gameTypeNotNullAndRankTypeNullForUserRankingReturnsError(GameType gameType)
      throws HttpException {

    if (gameType.equals(GameType.SINGLE)) {
      // this type is not applicable
      return;
    }

    GetRankingByProfileId getRankingByProfileIdBuilder = GetRankingByProfileId.builder()
        .userId(getCurrentTestUser().getUid())
        .gameType(gameType.getValue())
        .rankType(UserRankingTypes.NULL)
        .build();

    var getRankingFirstPlaceResponse =
        getRankingByProfileIdBuilder.getRankingByProfile();

    currentTestResponse.set(getRankingFirstPlaceResponse);

    getRankingFirstPlaceResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error." + MESSAGE_PROP,
            equalTo("rankType: Must be game or template."))
        .body("error." + STATUS_PROP, equalTo(STATUS_VALIDATION_ERROR));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1574")})
  @DisplayName("Get rankings for game when user has been excluded from the game")
  public void getUserRankingWhenPrecedingUserExcludedFromGame()
      throws Exception {

    var gameId = GamesEndpoint.createGames(GameType.TOP_X, 1).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 40,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(2).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 0,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()), 1, "1",
        predictionLastUpdate);

    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.SINGLE, 30,
        null,
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(4).getMatchId()), 1, "1",
        predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    //Get Leaderboard by game id before profileIdFirstPlace to be excluded from the game
    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    var getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(3))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace));

    //Update the game by excluding profileIdFirstPlace
    var updatedGameRequest = UpdateGameRequest.builder()
        .excludedProfileIds(List.of(profileIdFirstPlace))
        .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);


    updateGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("excluded_profile_ids", contains(profileIdFirstPlace));

    //Get Leaderboard by game id after profileIdFirstPlace to be excluded from the game
    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId(), 2);
    gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(2))
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }
}
