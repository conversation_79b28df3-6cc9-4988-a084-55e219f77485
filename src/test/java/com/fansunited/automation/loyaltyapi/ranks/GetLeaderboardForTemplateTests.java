package com.fansunited.automation.loyaltyapi.ranks;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_MAN_UTD;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.DEFAULT_PAGE_LIMIT;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.FROM_DATE_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.POINTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.POSITION_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.PROFILE_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.TO_DATE_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.UPDATED_AT_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.LoyaltyApi.Endpoints.Ranks.GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.loyaltyapi.LeaderboardByIdEndpoint.getLeaderboardForTemplateWithId;
import static com.fansunited.automation.core.base.AuthBase.createUser;
import static com.fansunited.automation.helpers.BigQueryHelper.waitDataToBeSavedInMySQL;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.helpers.FirebaseHelper.ARCHIVE_LEADERBOARD_COLLECTION;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.FirebaseHelper.GAME_COLLECTION;
import static com.fansunited.automation.helpers.FirebaseHelper.getFirestoreCollection;
import static com.fansunited.automation.helpers.FirebaseHelper.isEntityIdFromArchiveCollection;
import static com.fansunited.automation.helpers.FirebaseHelper.updateCollectionField;
import static com.fansunited.automation.helpers.FirebaseHelper.updateCollectionTimestampField;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;

import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplateByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.TemplateBaseTest;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.core.watchers.TestWatcherImpl;
import com.fansunited.automation.helpers.CustomHamcrestMatchers;
import com.fansunited.automation.helpers.DateTripletHelper;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import com.fansunited.automation.mappers.EventMapper;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.validators.CacheValidator;
import com.github.javafaker.Faker;
import com.google.cloud.Timestamp;
import com.google.cloud.firestore.CollectionReference;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Leaderboards should have unique matches, teams, competitions
 */
@DisplayName("Loyalty Api - GET /v1/leaderboard/{templateId} endpoint happy path tests")
public class GetLeaderboardForTemplateTests extends TemplateBaseTest {

  private static final Logger LOG = LoggerFactory.getLogger(GetLeaderboardForTemplateTests.class);
  private EventMapper mapper = new EventMapper();

  @AfterAll
  public static void cleanUp() {
    LOG.debug("Setting shouldTruncateTables to true");
    TestWatcherImpl.setShouldTruncateTables(true);
  }

  // 8 - 10 days
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get rankings for custom template with specific market")
  public void getRankingsForTemplateSpecificMarket() throws Exception {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var matchList = MatchGenerator.generateMatchesInFuture(1, 6);
    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    Resolver.openMatchesForPredictions(matchList);

    var markets =
        List.of(PredictionMarket.BOTH_TEAMS_SCORE.getValue());

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(dateTriplet.getFromString())
        .toDate(dateTriplet.getToString())
        .markets(markets)
        .build();

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.BOTH_TEAMS_SCORE, GameType.SINGLE,
        60, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.BOTH_TEAMS_SCORE, GameType.SINGLE,
        30, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));


    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.BOTH_TEAMS_SCORE, GameType.SINGLE,
        0, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var template = createTemplateResponse.as(TemplateResponse.class);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());

    var getLeaderboardResponse =
        getLeaderboardForTemplateWithId(template.getId(), null, null);

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }

  // 4 - 6 days
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get rankings for custom template with specific competition")
  public void getRankingsForTemplateSpecificComp() throws Exception {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var matchList = MatchGenerator.generateMatchesInFuture(2, 6);
    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    Resolver.openMatchesForPredictions(matchList);

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(dateTriplet.getFromString())
        .toDate(dateTriplet.getToString())
        .competitionIds(List.of(matchList.get(0).getCompetition().getId()))
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var template = createTemplateResponse.as(TemplateResponse.class);



    var profileIdFirstPlace = UUID.randomUUID().toString();

    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.HT_FT, GameType.SINGLE,
        60, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.HT_FT, GameType.SINGLE,
        60, null, mapper.hibernateMatchToAutomationMatch(matchList.get(1)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.HT_FT, GameType.SINGLE,
        30, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.HT_FT, GameType.SINGLE,
        30, null, mapper.hibernateMatchToAutomationMatch(matchList.get(1)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.HT_FT, GameType.SINGLE,
        0, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.HT_FT, GameType.SINGLE,
        0, null, mapper.hibernateMatchToAutomationMatch(matchList.get(1)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());

    var getLeaderboardResponse =
        getLeaderboardForTemplateWithId(template.getId(), null, null);

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }

  // 12 - 14
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get rankings for custom template with specific matches")
  public void getRankingsForTemplateSpecificMatches() throws Exception {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var matchList = MatchGenerator.generateMatchesInFutureDays(23, 13);
    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    Resolver.openMatchesForPredictions(matchList);
    var matchIds = matchList.stream().map(Match::getId).collect(Collectors.toList());

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(dateTriplet.getFromString())
        .toDate(dateTriplet.getToString())
        .matchIds(matchIds)
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var template = createTemplateResponse.as(TemplateResponse.class);

    var predictionLastUpdate =
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.OVER_GOALS_0_5,
        GameType.SINGLE, 30,
        null,
        MatchByIdEndpoint.getMatchDtoById(matchIds.get(0)), 1, "1", predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.OVER_GOALS_1_5,
        GameType.SINGLE, 30,
        null,
        MatchByIdEndpoint.getMatchDtoById(matchIds.get(1)), 1, "1", predictionLastUpdate);

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.OVER_GOALS_3_5,
        GameType.SINGLE, 30,
        null,
        MatchByIdEndpoint.getMatchDtoById(matchIds.get(2)), 1, "1", predictionLastUpdate);

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.OVER_GOALS_0_5,
        GameType.SINGLE, 0,
        null,
        MatchByIdEndpoint.getMatchDtoById(matchIds.get(3)), 1, "1", predictionLastUpdate);

    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.OVER_GOALS_0_5,
        GameType.SINGLE, 30,
        null,
        MatchByIdEndpoint.getMatchDtoById(matchIds.get(4)), 1,
        "1", predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());

    var getLeaderboardResponse =
        getLeaderboardForTemplateWithId(template.getId(), null, null);

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then().log().all()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }

  // 14 - 16
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get rankings for custom template with specific games")
  public void getRankingsForTemplateSpecificGame() throws Exception {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var matchList = MatchGenerator.generateMatchesInFuture(5, 6);
    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    Resolver.openMatchesForPredictions(matchList);

    var gameIds = GamesEndpoint.createGames(GameType.TOP_X, 1);

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(dateTriplet.getFromString())
        .toDate(dateTriplet.getToString())
        .gameIds(gameIds)
        .build();

    var gameInstance = GameEndpoint.getGameById(gameIds.get(0)).as(GameInstance.class);

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var template = createTemplateResponse.as(TemplateResponse.class);

    var predictionLastUpdate =
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(matchList.get(0).getId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(matchList.get(1).getId()), 1, "1",
        predictionLastUpdate);

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 40,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(matchList.get(2).getId()), 1, "1",
        predictionLastUpdate);

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 0,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(matchList.get(3).getId()), 1, "1",
        predictionLastUpdate);

    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.SINGLE, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(matchList.get(4).getId()), 1, "1",
        predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());

    var getLeaderboardResponse =
        getLeaderboardForTemplateWithId(template.getId(), null, null);

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }

  // -6 -4 days
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get rankings for custom template with specific teams")
  public void getRankingsForTemplateSpecificTeams() throws Exception {

    var dateTriplet = DateTripletHelper.getInstance().getNextNegative();
    var teamIds = List.of(TEAM_ID_LIVERPOOL, TEAM_ID_MAN_UTD);
    var fromDate = dateTriplet.getFromString();
    var toDate = dateTriplet.getToString();
    var matchList = MatchGenerator.generateMatchesInFuture(2, 6);
    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));

    Resolver.openMatchesForPredictions(matchList);

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(fromDate)
        .toDate(toDate)
        .teamIds(teamIds)
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var template = createTemplateResponse.as(TemplateResponse.class);
    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.OVER_CORNERS_7_5, GameType.SINGLE,
        20, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.OVER_CORNERS_7_5, GameType.SINGLE,
        20, null, mapper.hibernateMatchToAutomationMatch(matchList.get(1)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.OVER_CORNERS_7_5, GameType.SINGLE,
        10, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.OVER_CORNERS_7_5, GameType.SINGLE,
        10, null, mapper.hibernateMatchToAutomationMatch(matchList.get(1)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));


    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.OVER_CORNERS_7_5, GameType.SINGLE,
        0, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.OVER_CORNERS_7_5, GameType.SINGLE,
        0, null, mapper.hibernateMatchToAutomationMatch(matchList.get(1)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    matchList.forEach(match -> {
      Resolver.updateMatchKickOffAndFinishDates(match.getId(), dateTriplet.getMidLocalDateTime(),
          dateTriplet.getMidLocalDateTime(), MatchGenerator.STATUS_FINISHED);
    });
    Resolver.resolve(matchList.size());
    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());

   waitDataToBeSavedInMySQL("TEMPLATE", template.getId(), 10, 300);

    var getLeaderboardResponse =
        getLeaderboardForTemplateWithId(template.getId(), null, null);

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }

  // 16 18 days
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get rankings for custom template with game types")
  public void getRankingsForTemplateSpecificGameType() throws Exception {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDate = dateTriplet.getFromString();
    var toDate = dateTriplet.getToString();
    var markets  = List.of(PredictionMarket.FT_1X2);
    var gameIds = GamesEndpoint.createGames(GameType.MATCH_QUIZ, 1);
    var gameInstance = GameEndpoint.getGameById(gameIds.get(0)).as(GameInstance.class);
    var matchList = MatchGenerator.generateMatchesInFuture(2, 6);
    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    Resolver.openMatchesForPredictions(matchList);
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(fromDate)
        .toDate(toDate)
        .gameTypes(List.of(GameType.MATCH_QUIZ.getValue()))
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var template = createTemplateResponse.as(TemplateResponse.class);
    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE, GameType.MATCH_QUIZ,
        20, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE, GameType.MATCH_QUIZ,
        20, null, mapper.hibernateMatchToAutomationMatch(matchList.get(1)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE, GameType.MATCH_QUIZ,
        10, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE, GameType.MATCH_QUIZ,
        10, null, mapper.hibernateMatchToAutomationMatch(matchList.get(1)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE, GameType.MATCH_QUIZ,
        0, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE, GameType.MATCH_QUIZ,
        0, null, mapper.hibernateMatchToAutomationMatch(matchList.get(1)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId(), 3);

    var getLeaderboardResponse =
        getLeaderboardForTemplateWithId(template.getId(), null, null);

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }

  // 6 - 8 days
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get rankings for custom template with specific date range")
  public void getRankingsForTemplateSpecificDateRange() throws Exception {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var matchList = MatchGenerator.generateMatchesInFuture(1, 6);
    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    Resolver.openMatchesForPredictions(matchList);

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE, GameType.SINGLE,
        20, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));


    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE, GameType.SINGLE,
        10, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE, GameType.SINGLE,
        5, null, mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));

    var fromDate = dateTriplet.getFromString();
    var toDate = dateTriplet.getToString();

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(fromDate)
        .toDate(toDate)
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var template = createTemplateResponse.as(TemplateResponse.class);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());

    var getLeaderboardResponse =
        getLeaderboardForTemplateWithId(template.getId(), null, null);

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[0]." + POINTS_PROP, equalTo(20))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace))
        .body("meta." + FROM_DATE_PROP, equalTo(fromDate.toString()))
        .body("meta." + TO_DATE_PROP, equalTo(toDate.toString()))
        .body("meta.pagination.current_page", equalTo(1))
        .body("meta.pagination.items_per_page", equalTo(DEFAULT_PAGE_LIMIT))
        .body("meta.pagination.total_items", equalTo(3))
        .body("meta.pagination.number_of_pages", equalTo(1));
  }

  // 10 - 12
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify pagination for template works")
  public void getRankingsForTemplatePagination() throws Exception {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var matchList = MatchGenerator.generateMatchesInFuture(1, 6);
    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    Resolver.openMatchesForPredictions(matchList);

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(dateTriplet.getFromString())
        .toDate(dateTriplet.getToString())
        .matchIds(List.of(matchList.get(0).getId()))
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var template = createTemplateResponse.as(TemplateResponse.class);

    int numberOfEvents = 15;

    var predictionLastUpdate =
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    for (int i = 0; i < numberOfEvents; i++) {
      InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), UUID.randomUUID().toString(),
          PredictionMarket.RED_CARD_MATCH,
          GameType.SINGLE, Helper.generateRandomNumber(0, 300),
          null,
          MatchByIdEndpoint.getMatchDtoById(matchList.get(0).getId()), 1, "1", predictionLastUpdate);
    }

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());

    var getLeaderboardResponse =
        getLeaderboardForTemplateWithId(template.getId(), null, null);

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(DEFAULT_PAGE_LIMIT))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("meta.pagination.current_page", equalTo(1))
        .body("meta.pagination.number_of_pages",
            equalTo((int) Math.ceil((double) numberOfEvents / DEFAULT_PAGE_LIMIT)))
        .body("meta.pagination.total_items", equalTo(numberOfEvents));

    getLeaderboardForTemplateWithId(template.getId(), 2, null)
        .then()
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(numberOfEvents - DEFAULT_PAGE_LIMIT))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("meta.pagination.current_page", equalTo(2))
        .body("meta.pagination.items_per_page", equalTo(DEFAULT_PAGE_LIMIT));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/leaderboard/{templateId} response returned by the server is cached for 1h")
  public void verifyGetLeaderboardByIdIsCached() throws HttpException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(fromDateString)
        .toDate(toDateString)
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    var templateId = createTemplateResponse.as(TemplateResponse.class);

    var response =
        getLeaderboardForTemplateWithId(templateId.getId(), null, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }

  //Template with date 41-43
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get rankings for custom template with specific rules and flags")
  public void getRankingsForTemplateSpecificRulesAndFlags()
      throws Exception {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var rules = "test rules";
    var flags = List.of("Premire League", "Serie A, LaLiga");

    var matchList = MatchGenerator.generateMatchesInFutureDays(1, 42);
    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    Resolver.openMatchesForPredictions(matchList);

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(dateTriplet.getFromString())
        .toDate(dateTriplet.getToString())
        .rules(rules)
        .flags(flags)
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);
    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var template = createTemplateResponse.as(TemplateResponse.class);

    var predictionLastUpdate =
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.FT_1X2,
        GameType.SINGLE, 30,
        null,
        mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1", predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.FT_1X2,
        GameType.SINGLE, 30,
        null,
        mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1", predictionLastUpdate);

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(dateTriplet.getMidLocalDateTime(),
        profileIdSecondPlace,
        PredictionMarket.FT_1X2,
        GameType.SINGLE, 30,
        null,
        mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1", predictionLastUpdate);

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(),
        profileIdThirdPlace,
        PredictionMarket.FT_1X2,
        GameType.SINGLE, 0,
        null,
        mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1", predictionLastUpdate);
    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());
    var getLeaderboardResponse =
        getLeaderboardForTemplateWithId(template.getId(), null, null);

    currentTestResponse.set(getLeaderboardResponse);
    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }

  // -10 -12
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1943")})
  @DisplayName("Verify custom template is archived when expired")
  public void verifyTemplateArchivedAfterExpiration()
      throws HttpException, InterruptedException, IOException, ExecutionException,
      FirebaseAuthException {

    //Create Template that had expired a day ago
    var markets =
        List.of(PredictionMarket.CORRECT_SCORE.getValue());

    var dateTriplet = DateTripletHelper.getInstance().getNextNegative();
    var startDate = dateTriplet.getFromString();
    var endDate = dateTriplet.getToString();

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(startDate)
        .toDate(endDate)
        .markets(markets)
        .build();

    var templateInstance =
        TemplatesEndpoint.createLeaderboardTemplate(templateRequest).as(TemplateResponse.class);

    //Create Single Prediction without the need to be resolved
    var goalsHome = 4;
    var goalsAway = 3;

    var match = MatchGenerator.generateMatchesInFuture(1, 1).get(0);
    match.setGoalsFullTimeHome((byte) goalsHome);
    match.setGoalsFullTimeAway((byte) goalsAway);

    Resolver.openMatchForPredictions(match);

    var createPredictionRequest = CreatePredictionRequest.builder().fixtures(List.of(
        CorrectScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsHome(goalsHome)
            .goalsAway(goalsAway)
            .build())).build();

    PredictionsEndpoint.createPredictionForUser(createPredictionRequest, createUser().getEmail())
        .then()
        .assertThat()
        .statusCode(
            HttpStatus.SC_OK);

    //Important is to have the match moved to the past - within the template timeframe
    Resolver.updateMatchKickOffAndFinishDates(match.getId(), dateTriplet.getMidLocalDateTime(),
        dateTriplet.getMidLocalDateTime(), MatchGenerator.STATUS_FINISHED);

    //By getting the Leaderboard by Template Id, an Archive is created in Firebase and the Template is stored there
    getLeaderboardForTemplateWithId(templateInstance.getId(), 1, 100);

    //The Archive creation takes a few seconds in order to appear in Firebase
    Thread.sleep(5000);

    var expectedArchivedTemplateId = templateInstance.getId();
    var actualDocument =
        FirebaseHelper.getEntityFromFirebaseCollection(FANS_UNITED_PROFILE,
            ARCHIVE_LEADERBOARD_COLLECTION, expectedArchivedTemplateId, "id");
    var realId = actualDocument.get("id").toString();

    Assertions.assertEquals(expectedArchivedTemplateId, realId);
  }

  // 2 - 4 days
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1574")})
  @DisplayName("Get rankings for template when user has been removed from the template")
  public void verifyLeaderboardByTemplateIdWhenUserRemovedFromTemplate()
      throws Exception {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);
    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    Resolver.openMatchesForPredictions(matchList);
    var gameId = GamesEndpoint.createGames(GameType.TOP_X, 1).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(dateTriplet.getFromString())
        .toDate(dateTriplet.getToString())
        .markets(List.of("CORRECT_SCORE"))
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    createTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var template = createTemplateResponse.as(TemplateResponse.class);

    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        mapper.hibernateMatchToAutomationMatch(matchList.get(0)), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        mapper.hibernateMatchToAutomationMatch(matchList.get(1)), 1, "1",
        predictionLastUpdate);

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 40,
        gameInstance.getId(),
        mapper.hibernateMatchToAutomationMatch(matchList.get(2)), 1, "1",
        predictionLastUpdate);

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 0,
        gameInstance.getId(),
        mapper.hibernateMatchToAutomationMatch(matchList.get(3)), 1, "1",
        predictionLastUpdate);

    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.SINGLE, 30,
        null,
        mapper.hibernateMatchToAutomationMatch(matchList.get(4)), 1, "1",
        predictionLastUpdate);

    //Important is to have the match moved to the past
    matchList.forEach(match -> {
      Resolver.updateMatchKickOffAndFinishDates(match.getId(), dateTriplet.getMidLocalDateTime(),
          dateTriplet.getMidLocalDateTime(), MatchGenerator.STATUS_FINISHED);
    });
    Resolver.resolve(matchList.size());

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());

    //Get Leaderboard by template id before profileIdFirstPlace to be excluded from the game
    var getLeaderboardResponse =
        getLeaderboardForTemplateWithId(template.getId(), null, null);

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));

    //Update the template by excluding profileIdFirstPlace
    templateRequest.setExcludedProfileIds(List.of(profileIdFirstPlace,profileIdSecondPlace));
    var updateTemplateResponse =
        TemplateByIdEndpoint.updateLeaderboardTemplate(template.getId(), templateRequest);

    updateTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("excluded_profile_ids", contains(profileIdFirstPlace,profileIdSecondPlace));

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            template.getId());

    //Get Leaderboard by template id after profileIdFirstPlace to be excluded from the template
    var leaderboardForTemplateWithId = getLeaderboardForTemplateWithId(template.getId(), 1, 100);

    leaderboardForTemplateWithId
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(1))
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }

  // -4 -2 days
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1574")})
  @DisplayName("Verify user removed from archived template")
  public void verifyUserExcludedFromArchivedTemplate()
      throws HttpException, InterruptedException, IOException, ExecutionException,
      FirebaseAuthException {

    UserRecord user = createUser();
    var markets = List.of(PredictionMarket.CORRECT_SCORE.getValue());
    var dateTriplet = DateTripletHelper.getInstance().getNextNegative();
    var startDateTemplate = dateTriplet.getFromString();
    var endDateTemplate = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(startDateTemplate)
        .toDate(endDateTemplate)
        .markets(markets)
        .build();

    var templateInstance =
        TemplatesEndpoint.createLeaderboardTemplate(templateRequest).as(TemplateResponse.class);

    //Create Game
    var endDateGame = dateTriplet.getFromLocalDateTime();
    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);

    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    matchList.forEach(match -> {
      match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
      match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
    });

    Resolver.openMatchesForPredictions(matchList);

    var gameInstance =
        GamesEndpoint.createGame(matchList.stream().map(Match::getId).toList(), GameType.TOP_X,
                GameStatus.OPEN, matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(match -> predictionFixtures.add(CorrectScorePredictionFixture.builder()
        .matchId(match.getId())
        .matchType(MatchType.FOOTBALL.getValue())
        .goalsHome(match.getGoalsFullTimeHome())
        .goalsAway(match.getGoalsFullTimeAway() + 1)
        .build()));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .gameInstanceId(gameInstance.getId()).fixtures(predictionFixtures).build();

    PredictionsEndpoint.createPredictionForUser(createPredictionRequest, user.getEmail());

    //Important is to have the match moved to the past
    matchList.forEach(match -> {
      Resolver.updateMatchKickOffAndFinishDates(match.getId(), dateTriplet.getMidLocalDateTime(),
          dateTriplet.getMidLocalDateTime(), MatchGenerator.STATUS_FINISHED);
    });

    //Close the game in order to be able to move to Settled Status
    updateCollectionField(
        getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION), gameInstance.getId(),
        STATUS_PROP, GameStatus.CLOSED.getValue());

    Resolver.resolve(matchList.size());

    //update Game updated_at field in Firestore
    updateCollectionTimestampField(
        getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION), gameInstance.getId(),
        UPDATED_AT_PROP, Timestamp.parseTimestamp(String.valueOf(endDateGame)));

    // This means just refresh of mv, without waiting for specific id to be ready. In this test, we will wait for readiness here.
    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV_NO_WAIT,
            "dummyid");

    waitForArchiveToBeFilled(templateInstance.getId());

    //By getting the Leaderboard by Template Id, an Archive is created in Firebase and the Template is stored there
    Response leaderboardForTemplateWithId =
        getLeaderboardForTemplateWithId(templateInstance.getId(), 1, 100);
    currentTestResponse.set(leaderboardForTemplateWithId);
    CollectionReference firestoreCollection =
        getFirestoreCollection(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION);

    var expectedArchivedTemplateId = templateInstance.getId();
    Assertions.assertTrue(
        isEntityIdFromArchiveCollection(firestoreCollection, expectedArchivedTemplateId));

    leaderboardForTemplateWithId
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(1))
        .body("data[0]." + PROFILE_ID_PROP, equalTo(user.getUid()));

    templateRequest.setExcludedProfileIds(List.of(user.getUid()));
    var updateTemplateResponse =
        TemplateByIdEndpoint.updateLeaderboardTemplate(templateInstance.getId(), templateRequest);
    currentTestResponse.set(updateTemplateResponse);

    updateTemplateResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("excluded_profile_ids", contains(user.getUid()));

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV_NO_WAIT,
            templateInstance.getId());

    waitForArchiveToBeDumped(templateInstance.getId());
    //Get Leaderboard by game id after profileIdFirstPlace to be excluded from the game
    leaderboardForTemplateWithId =
        getLeaderboardForTemplateWithId(templateInstance.getId(), 1, 100);

    currentTestResponse.set(leaderboardForTemplateWithId);

    leaderboardForTemplateWithId
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(0));
  }

  private void waitForArchiveToBeFilled(String templateId)
      throws IOException, ExecutionException, InterruptedException, HttpException {
    for (int i = 0; i < 30; i++) {
      FirebaseHelper.deleteDocument(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION, templateId);
      Response response = getLeaderboardForTemplateWithId(templateId, 1, 100)
          .then()
          .extract()
          .response();
      List<?> data = response.jsonPath().getList("data");
      if (data != null && !data.isEmpty()) {
        FirebaseHelper.deleteDocument(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION, templateId);
        LOG.info("Archive {} was created successfully", templateId);
        return;
      }
      Thread.sleep(10 * 1000);
    }
    LOG.error("Archive {} was NOT created successfully", templateId);
  }

  private void waitForArchiveToBeDumped(String templateId)
      throws IOException, ExecutionException, InterruptedException, HttpException {
    for (int i = 0; i < 30; i++) {
      FirebaseHelper.deleteDocument(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION, templateId);
      Response response = getLeaderboardForTemplateWithId(templateId, 1, 100)
          .then()
          .extract()
          .response();
      List<?> data = response.jsonPath().getList("data");

      if (data != null && data.isEmpty()) {
        FirebaseHelper.deleteDocument(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION, templateId);
        LOG.info("Archive {} was dumped successfully", templateId);
        return;
      }
      Thread.sleep(10 * 1000);
    }
    LOG.error("Archive {} was NOT dumped successfully", templateId);
  }
}
