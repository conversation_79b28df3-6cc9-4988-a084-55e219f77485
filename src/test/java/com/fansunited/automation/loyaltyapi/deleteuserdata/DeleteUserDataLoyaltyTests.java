package com.fansunited.automation.loyaltyapi.deleteuserdata;

import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.CURRENT_PROFILE_ID;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.NEW_PROFILE_ID;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.predictionapi.GamesEndpoint.createGames;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.helpers.Helper.generateMD5;
import static com.fansunited.automation.helpers.WaitHelper.waitProfileIDToBeUpdated;
import static com.fansunited.automation.helpers.bq.InsertBigQData.insertExcluded;
import static com.fansunited.automation.helpers.bq.InsertBigQData.insertProfileRow;
import static com.fansunited.automation.mappers.EventMapper.EVENT_TABLE;
import static com.fansunited.automation.mappers.EventMapper.EXCLUDED_TABLE;
import static com.fansunited.automation.mappers.EventMapper.PROFILE_TABLE;
import static com.fansunited.automation.mappers.EventMapper.RANK_TABLE;
import static com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType.TOP_X;
import static com.fansunited.automation.validators.LoyaltyApiValidator.verifyProfileIdChanged;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.DeleteUserDataEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.reportingapi.helper.InterestGenerator;
import com.fansunited.automation.core.apis.reportingapi.helper.ProfileGenerator;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.helpers.bq.profile.Profile;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Verify DELETE loyalty-api /v1/user-data happy path")
public class DeleteUserDataLoyaltyTests extends AuthBase {

  private static final String DELETED_PREFIX = "deleted_";

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify that the profile ID in 'profile' table is changed after sending request to /v1/user-data")
  public void deleteProfileIdFromProfileTable() throws InterruptedException, HttpException {

    // Insert profile to BQ
    Profile profile = insertProfileRow();
    var currentProfileId = profile.getId();
    waitProfileIDToBeUpdated(currentProfileId, PROFILE_TABLE, 2, 10);
    var newProfileId = DELETED_PREFIX + generateMD5(profile.getId());

    var body = createUserDataBody(currentProfileId, newProfileId);

    DeleteUserDataEndpoint.deleteUserDataEndpoint(body).then().assertThat().statusCode(200);

    verifyProfileIdChanged(PROFILE_TABLE, currentProfileId, newProfileId);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify that the profile ID in 'event' table is changed after sending request to /v1/user-data")
  public void deleteProfileIdFromEventTable() throws HttpException, InterruptedException {

    var currentProfileId = insertProfileRow().getId();
    var newProfileId = DELETED_PREFIX + generateMD5(currentProfileId);
    waitProfileIDToBeUpdated(currentProfileId, PROFILE_TABLE, 2, 10);

    LocalDateTime timestamp = LocalDateTime.now(ZoneOffset.UTC).minusMinutes(90);
    // Insert event to BQ
    InsertBigQData.generateCorrectPredictionEvent(
        timestamp,
        currentProfileId,
        UUID.randomUUID().toString(),
        TOP_X,
        PredictionMarket.CORRECT_SCORE,
        10);

    waitProfileIDToBeUpdated(currentProfileId, EVENT_TABLE, 2, 10);

    var body = createUserDataBody(currentProfileId, newProfileId);

    DeleteUserDataEndpoint.deleteUserDataEndpoint(body).then().assertThat().statusCode(200);

    verifyProfileIdChanged(EVENT_TABLE, currentProfileId, newProfileId);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify that the profile ID in 'rank' table is changed after sending request to /v1/user-data")
  public void deleteProfileIdFromRankTable() throws HttpException, InterruptedException {

    var currentProfileId =
        ProfileGenerator.generateProfiles(
                1,
                List.of(
                    InterestGenerator.generateFootballInterest(
                        ApiConstants.ProfileApi.Interest.Football.TEAM, true)))
            .get(0)
            .getId();

    var newProfileId = DELETED_PREFIX + generateMD5(currentProfileId);
    var gameId = createGames(GameType.TOP_X, 1).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var predictionLastUpdate =
        LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    LocalDateTime timestamp = LocalDateTime.now(ZoneOffset.UTC).minusMinutes(90);
    // Insert rank to BQ
    InsertBigQData.insertSingleRankEvent(
        timestamp,
        currentProfileId,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X,
        30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        1,
        "1",
        predictionLastUpdate);

    waitProfileIDToBeUpdated(currentProfileId, RANK_TABLE, 2, 10);

    var body = createUserDataBody(currentProfileId, newProfileId);

    DeleteUserDataEndpoint.deleteUserDataEndpoint(body).then().assertThat().statusCode(200);

    verifyProfileIdChanged(RANK_TABLE, currentProfileId, newProfileId);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify that the profile ID in 'excluded' table is changed after sending request to /v1/user-data")
  public void deleteProfileIdFromExcludedTable() throws HttpException {

    // Insert new profile to BQ
    var currentProfileId = UUID.randomUUID().toString();
    var newProfileId = DELETED_PREFIX + generateMD5(currentProfileId);

    // Insert data into the 'excluded' table
    insertExcluded(currentProfileId);

    var body = createUserDataBody(currentProfileId, newProfileId);

    DeleteUserDataEndpoint.deleteUserDataEndpoint(body).then().assertThat().statusCode(200);

    verifyProfileIdChanged(EXCLUDED_TABLE, currentProfileId, newProfileId);
  }

  /**
   * Creates a JSON object containing the current and new profile IDs.
   *
   * @param currentProfileId The current profile ID to be changed
   * @param newProfileId The new profile ID to replace the current one
   * @return JSONObject containing the mapping between current and new profile IDs
   */
  @SuppressWarnings("unchecked")
  public static JSONObject createUserDataBody(String currentProfileId, String newProfileId) {
    JSONObject body = new JSONObject();
    body.put(CURRENT_PROFILE_ID, currentProfileId);
    body.put(NEW_PROFILE_ID, newProfileId);
    return body;
  }
}
