package com.fansunited.automation.loyaltyapi;

import static com.fansunited.automation.constants.Endpoints.LoyaltyApi.ACTIVITIES;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.loyaltyapi.OptionsLoyaltyEndpoint.optionsLoyaltyApi;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.base.AuthBase;
import io.restassured.response.Response;
import java.util.Arrays;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

@DisplayName("Loyalty API - OPTIONS method")
public class LoyaltyOptionsMethodTest extends AuthBase {

  @Test
  @DisplayName(
      "Verify Loyalty API using the OPTIONS method. Endpoint: Loyalty OPTIONS /v1/activities")
  @Tag(SMOKE)
  public void optionsMethodLoyaltyTest() throws HttpException {
    Response response = optionsLoyaltyApi(ACTIVITIES);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    var actualMethods =
        Arrays.stream(response.getHeaders().getValues("Allow").get(0).split(", ")).toList();
    var expectedMethods = ApiConstants.HttpMethods.getValuesAsList();

    assertThat(actualMethods).as(EMPTY_LIST_MESSAGE).isNotEmpty().isNotNull();
    Assertions.assertTrue(expectedMethods.containsAll(actualMethods), METHODS_MISMATCH_MESSAGE);
  }
}
