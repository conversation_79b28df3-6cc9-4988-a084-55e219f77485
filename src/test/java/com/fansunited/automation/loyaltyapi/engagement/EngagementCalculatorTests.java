package com.fansunited.automation.loyaltyapi.engagement;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.TestGroups.STAGE;

import com.fansunited.automation.core.base.loyaltyapi.ActivityBaseTest;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Loyalty Api - POST /v1/activity endpoint happy path tests")
public class EngagementCalculatorTests extends ActivityBaseTest {

  @Test
  @DisplayName("Verify creation of activity for action")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(STAGE)})
  public void createActivities()
      throws Exception {

    InsertBigQData.insertProfiles(5);
    InsertBigQData.insertEvents(5,"QUIZ_PARTICIPATION","quiz","mini-games-api","PARTICIPATE");
    InsertBigQData.insertRankEvent(5, PredictionMarket.CORRECT_SCORE,"test1234",5,"test123","TOP_X",true);

    Thread.sleep(1000);
  }
}
