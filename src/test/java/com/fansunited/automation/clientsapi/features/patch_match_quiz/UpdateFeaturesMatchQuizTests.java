
package com.fansunited.automation.clientsapi.features.patch_match_quiz;

import static com.fansunited.automation.constants.AuthConstants.DEFAULT_USER_PASS;
import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_MATCH_QUIZ;
import static com.fansunited.automation.constants.JsonSchemasPath.ClientsApi.Endpoints.Clients.GET_CLIENT_BY_ID_MATCH_QUIZ_FEATURES_SCHEMA;
import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static java.util.List.of;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.apis.clientapi.StaffMemberEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.CreateStaffMember;
import com.fansunited.automation.model.clientapi.features.response.AssetsURL;
import com.fansunited.automation.model.clientapi.features.response.MatchQuizFeature;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Client Api - GET /v1/client/{clientId}/feature endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateFeaturesMatchQuizTests extends ClientApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify client by Id features endpoint match to swagger schema definition")
  public void checkClientByIdFeature() throws HttpException {

    var staff = CreateStaffMember.builder()
        .email(new Faker().internet().emailAddress())
        .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
        .pass(DEFAULT_USER_PASS)
        .roles(of("client_product_manager"))
        .build();
    var createStaffMember =
        StaffMemberEndpoint.createStaff(FANS_UNITED_CLIENTS,
            staff, CLIENT_AUTOMATION_ID);
    currentTestResponse.set(createStaffMember);
    createStaffMember
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var bodyMatchQuiz = MatchQuizFeature.builder()
        .enabled(true)
        .assets(List.of(AssetsURL.builder().main_image_url("test").build()))
        .defaultMarkets(PredictionMarket.getValidMarkets())
        .competitionsWhitelist(
            of("fb:c:1", "fb:c:3"))
        .build();

    var updateTopXFeatures =
        ClientFeaturesEndpoint.updateFeatures(FANS_UNITED_CLIENTS, staff.getEmail(),
            bodyMatchQuiz,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, FEATURES_MATCH_QUIZ);

    currentTestResponse.set(updateTopXFeatures);

    updateTopXFeatures
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
                GET_CLIENT_BY_ID_MATCH_QUIZ_FEATURES_SCHEMA));
  }
}
