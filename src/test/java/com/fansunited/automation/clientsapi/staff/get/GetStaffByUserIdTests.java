package com.fansunited.automation.clientsapi.staff.get;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.AuthConstants.DEFAULT_USER_PASS;
import static com.fansunited.automation.constants.JsonSchemasPath.ClientsApi.Endpoints.Staff.GET_STAFF_BY_CLIENTS_ID_SCHEMA;
import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.StaffMemberEndpoint;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.CreateStaffMember;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Client Api - GET /v1/clients/{client_id}/staff endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetStaffByUserIdTests extends BaseTest {

  public String userId;
  public String email;

  @Before
  public void createStaff() throws HttpException {

    var createStaff =
        StaffMemberEndpoint.createStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CreateStaffMember.builder()
                .email(new Faker().internet().emailAddress())
                .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
                .pass(DEFAULT_USER_PASS)
                .roles(List.of("client_editor"))
                .build(), CLIENT_AUTOMATION_ID);

    createStaff
        .then()
        .log().body()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    userId = createStaff.then().extract().body().jsonPath().getString("data.id");
    email = createStaff.then().extract().body().jsonPath().getString("data.email");
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify get staff by user Id endpoint match to swagger schema definition")
  public void checkStaffById() throws HttpException {

    var response =
        StaffMemberEndpoint.getStaffByUserId(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            userId, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + ID_PROP, equalTo(CLIENT_AUTOMATION_ID))
        .body("data[1].", hasValue(0))
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_STAFF_BY_CLIENTS_ID_SCHEMA));
  }
}
