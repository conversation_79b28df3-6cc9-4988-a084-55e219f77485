package com.fansunited.automation.clientsapi.staff.post;

import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.StaffMemberEndpoint;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.CreateStaffMember;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Client Api - POST /v1/clients/{client_id}/staff endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class CreateStaffTests extends BaseTest {

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify creation of staff members")
  public void createStaffWithDiffRoles()
      throws IllegalArgumentException, HttpException {

    var createStaffRequest = CreateStaffMember.builder()
        .email(new Faker().internet().emailAddress())
        .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
        .pass(new Faker().internet().password())
        .roles(List.of("client_editor"))
        .build();

    var response =
        StaffMemberEndpoint.createStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            createStaffRequest, CLIENT_AUTOMATION_ID);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var userId = response.then().extract().body().jsonPath().getString("data.id");

    var getStaffMembers= StaffMemberEndpoint.getStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    getStaffMembers
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var deleteStaffMembers =
        StaffMemberEndpoint.deleteStaffMembers(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, CLIENT_AUTOMATION_ID,
            userId, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    deleteStaffMembers
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var getStaffMembersAg= StaffMemberEndpoint.getStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    getStaffMembersAg
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify creation of staff members with billing permission")
  public void createStaffBillingPermission()
      throws IllegalArgumentException, HttpException {

    var createStaffRequest = CreateStaffMember.builder()
        .email(new Faker().internet().emailAddress())
        .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
        .pass(new Faker().internet().password())
        .roles(List.of("client_billing_manager"))
        .build();

    FirebaseHelper.getUserTokenMap()
        .clear();
    var response =
        StaffMemberEndpoint.createStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            createStaffRequest, CLIENT_AUTOMATION_ID);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }
}
