package com.fansunited.automation.clientsapi.profile;

import static com.fansunited.automation.constants.AuthConstants.DEFAULT_USER_PASS;
import static com.fansunited.automation.constants.JsonSchemasPath.ClientsApi.Endpoints.Profile.GET_PROFILE_FOR_USER_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.LOCAL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientProfileEndpoint;
import com.fansunited.automation.core.apis.clientapi.StaffMemberEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.CreateStaffMember;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Client Api - GET /v1/profile endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetProfileTests extends ClientApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(LOCAL)})
  @DisplayName("Verify client by Id endpoint match to swagger schema definition")
  public void checkProfile() throws HttpException {

    var createStaffRequestBody = CreateStaffMember.builder()
        .email(new Faker().internet().emailAddress())
        .name(new Faker().funnyName().name())
        .pass(DEFAULT_USER_PASS)
        .roles(List.of("client_admin"))
        .build();
    StaffMemberEndpoint.createStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        createStaffRequestBody, CLIENT_AUTOMATION_ID);

    var response =
        ClientProfileEndpoint.getProfile(AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_PROFILE_FOR_USER_SCHEMA));
  }
}
