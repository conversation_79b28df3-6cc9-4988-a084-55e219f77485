package com.fansunited.automation.minigames.eitheror.patch;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.eitherOr.UpdateEitherOrEndpoint.updateEitherOr;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.oneOf;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrWinningCondition;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@Execution(ExecutionMode.SAME_THREAD)
public class UpdateEitherOrValidationTest extends MiniGamesApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify user can not update trivia game")
  public void updateEitherOrHappyPath()
      throws IllegalArgumentException,
          HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    var user = createUser();

    var id = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();

    var response =
        updateEitherOr(
            updateTriviaGameRequest(EitherOrWinningCondition.MORE, CommonStatus.ACTIVE),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            user.getEmail(),
            id);

    response.then().assertThat().statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @ParameterizedTest(
      name = "Verify can not update trivia game with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void updateEitherOrWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var id = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();

    var response =
        updateEitherOr(
            updateTriviaGameRequest(EitherOrWinningCondition.LESS, CommonStatus.ACTIVE),
            CLIENT_AUTOMATION_ID,
            String.valueOf(argumentsHolder),
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null,
            id);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when updating trivia game for client with action filter. Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_ID)
  @NullAndEmptySource
  public void updateEitherOrInvalidClientId(String clientId) throws HttpException {

    var id = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();

    var response =
        updateEitherOr(
            updateTriviaGameRequest(EitherOrWinningCondition.MORE, CommonStatus.ACTIVE),
            clientId,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null,
            id);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(
        response, oneOf(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @Test
  @DisplayName("Update trivia game with not specified content type")
  @Tag(REGRESSION)
  public void updateEitherOrWithWrongContentTypeTest()
      throws IllegalArgumentException, HttpException {

    var id = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();

    var response =
        updateEitherOr(
            updateTriviaGameRequest(EitherOrWinningCondition.MORE, CommonStatus.ACTIVE),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null,
            FANS_UNITED_CLIENTS,
            null,
            id);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo("invalid_content_type"));
  }
}
