package com.fansunited.automation.minigames.eitheror.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.eitherOr.GetEitherOrByIdEndpoint.getEitherOrById;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrWinningCondition;
import com.fansunited.automation.validators.CacheValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Get trivia game by id happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class GetEitherOrByIdTest extends MiniGamesApiBaseTest {


  @Test
  @DisplayName("Get either/or happy path")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getEitherOrHappyPath() throws IllegalArgumentException, HttpException {

    var id = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();

    var response =
        getEitherOrById(
            id,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    response.then().assertThat().statusCode(HttpStatus.SC_OK)
        .log().ifValidationFails()
        .body("data.id",equalTo(id))
        .body("data.title",equalTo("Only for test"));
  }

  @Test
  @DisplayName("Get either/or returns 1h cache")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getEitherOrCache() throws HttpException {

    var id = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();

    var response =
        getEitherOrById(
            id,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    response.then().assertThat().statusCode(HttpStatus.SC_OK)
        .body("data.id",equalTo(id))
        .body("data.title",equalTo("Only for test"));

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.ONE_HOUR);

  }

}
