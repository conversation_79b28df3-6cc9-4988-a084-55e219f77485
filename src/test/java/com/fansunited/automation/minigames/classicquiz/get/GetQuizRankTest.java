package com.fansunited.automation.minigames.classicquiz.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.GetQuizRankEndpoint.getQuizRank;
import static com.fansunited.automation.core.apis.minigames.classicquiz.ParticipateQuizEndpoint.participateQuiz;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@Execution(ExecutionMode.SAME_THREAD)
public class GetQuizRankTest extends MiniGamesApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify get classic quiz rank")
  public void getClassicQuizRankTest()
      throws IllegalArgumentException,
          HttpException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException {

    var userIds = createUsers(4);
    var quizIds=createQuizzes(3);

    var participationInFirstQuiz =
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 1),
            quizIds.get(0),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            userIds.get(0).getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationInFirstQuiz.then().assertThat().statusCode(200);

    var participationInSecondQuiz =
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 1),
            quizIds.get(1),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            userIds.get(0).getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationInSecondQuiz.then().assertThat().statusCode(200);

    var participationFirstUserResponse =
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 2),
            quizIds.get(0),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            userIds.get(1).getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationFirstUserResponse.then().assertThat().statusCode(200);

    var participationSecondUserResponse =
        participateQuiz(
            participateClassicQuizRequest(1, 2, 2, 2),
            quizIds.get(0),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            userIds.get(2).getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationSecondUserResponse.then().assertThat().statusCode(200);

    var response =
        getQuizRank(
            quizIds.get(0) + "," + quizIds.get(1),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0].profile_id", equalTo(userIds.get(0).getUid()))
        .body("data[1].profile_id", equalTo(userIds.get(1).getUid()))
        .body("data[2].profile_id", equalTo(userIds.get(2).getUid()));
  }


  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify get classic quiz ranking when the users are on the same position")
  public void getClassicQuizRankOnTheSamePositionTest()
      throws IllegalArgumentException,
      HttpException,
      IOException,
      ExecutionException,
      FirebaseAuthException,
      InterruptedException {

    var userIds = createUsers(4);
    var quizIds=createQuizzes(3);

    var participationInFirstQuiz =
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 1),
            quizIds.get(0),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            userIds.get(0).getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationInFirstQuiz.then().assertThat().statusCode(200);

    var participationInSecondQuiz =
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 1),
            quizIds.get(0),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            userIds.get(0).getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationInSecondQuiz.then().assertThat().statusCode(200);

    var participationFirstUserResponse =
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 1),
            quizIds.get(0),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            userIds.get(1).getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationFirstUserResponse.then().assertThat().statusCode(200);

    var participationSecondUserResponse =
        participateQuiz(
            participateClassicQuizRequest(1, 2, 2, 2),
            quizIds.get(1),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            userIds.get(2).getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationSecondUserResponse.then().assertThat().statusCode(200);

    var response =
        getQuizRank(
            quizIds.get(0) + "," + quizIds.get(1),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0].profile_id", equalTo(userIds.get(1).getUid()))
        .body("data[0].position",equalTo(1))
        .body("data[1].profile_id", equalTo(userIds.get(0).getUid()))
        .body("data[1].position",equalTo(1))
        .body("data[2].profile_id", equalTo(userIds.get(2).getUid()))
        .body("data[2].position",equalTo(3));
  }
}
