package com.fansunited.automation.minigames.classicquiz.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.BILLING_MANAGER_EMAIL;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.GetQuizByStaffEndpoint.getQuizAsStaff;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.oneOf;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Get quiz as a staff member validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetQuizAsStaffValidationTest extends MiniGamesApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify billing manager can not get classic quiz as a staff member")
  public void getClassicQuizAsStaffTest() throws IllegalArgumentException, HttpException {

    var quizResponse = createQuizForTest(CommonStatus.ACTIVE);

    var quizId = quizResponse.getData().getId();

    var response = getQuizAsStaff(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, BILLING_MANAGER_EMAIL, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify user can not get classic quiz as a staff member")
  public void getClassicQuizAsUserTest() throws IllegalArgumentException, HttpException {

    var quizResponse = createQuizForTest(CommonStatus.ACTIVE);

    var quizId = quizResponse.getData().getId();

    var response = getQuizAsStaff(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_PROFILE, getCurrentTestUser().getEmail(), AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when get quiz as staff member with invalid client id action filter. Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @EmptySource
  public void getQuizAsStaffWithInvalidClientId(String clientId)
      throws HttpException {

    var response = getQuizAsStaff("quizId", clientId,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response,
        oneOf(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when get quiz as staff member with invalid quiz id action filter. Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_ID)
  @EmptySource
  public void getQuizAsStaffWithInvalidQuizId(String quizId)
      throws HttpException {

    var response = getQuizAsStaff(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response,
        oneOf(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_NOT_FOUND));
  }

  @ParameterizedTest(name = "Verify cannot get quiz as staff with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getQuizAsStaffWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response = getQuizAsStaff("quizId", CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }














}
