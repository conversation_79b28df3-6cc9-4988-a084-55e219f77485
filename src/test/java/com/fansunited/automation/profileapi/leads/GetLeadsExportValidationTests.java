package com.fansunited.automation.profileapi.leads;

import static com.fansunited.automation.constants.ApiErrorCodes.INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_FORBIDDEN;
import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CLIENT;
import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_UNAUTHORIZED_ERROR;
import static com.fansunited.automation.constants.AuthConstants.ENDPOINTS_API_KEY;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.profileapi.leads.GetExportLeadsEndpoint.getExportLeads;
import static org.apache.http.HttpStatus.SC_FORBIDDEN;
import static org.hamcrest.CoreMatchers.anyOf;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.Matchers.in;

import com.fansunited.automation.arguments.clientapi.InvalidArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Profile Api - POST /v1/leads/export endpoint validation tests")
public class GetLeadsExportValidationTests extends ProfileApiBaseTest {

  @ParameterizedTest(name = "Verify leads export fails with invalid API key: {0}")
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @Tag(REGRESSION)
  @Tag(SMOKE)
  public void getLeadsExportWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {
    var response =
        getExportLeads(
            null,
            null,
            null,
            null,
            null,
            ADMIN_USER,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            true,
            null);

    response.then().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify leads export fails with invalid JWT token: {0}")
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  @Tag(REGRESSION)
  @Tag(SMOKE)
  public void getLeadsExportWithInvalidToken(InvalidJwtTokenArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        getExportLeads(
            null,
            null,
            null,
            null,
            null,
            null,
            CLIENT_AUTOMATION_ID,
            ENDPOINTS_API_KEY,
            ContentType.JSON,
            true,
            argumentsHolder.getJwtToken());

    ErrorValidator.validateErrorResponseEmptyBody(
        response, List.of(HttpStatus.SC_UNAUTHORIZED, SC_FORBIDDEN));

    response
        .then()
        .body("error.status", anyOf(equalTo(CODE_UNAUTHORIZED_ERROR), equalTo(CODE_FORBIDDEN)));
  }

  @Test
  @DisplayName("Verify leads export fails without JWT token")
  @Tag(REGRESSION)
  @Tag(SMOKE)
  public void getLeadsExportWithoutToken() throws HttpException {
    var response =
        getExportLeads(
            null,
            null,
            null,
            null,
            null,
            null,
            CLIENT_AUTOMATION_ID,
            ENDPOINTS_API_KEY,
            ContentType.JSON,
            false,
            null);

    response
        .then()
        .statusCode(HttpStatus.SC_UNAUTHORIZED)
        .body("error.status", equalTo(CODE_UNAUTHORIZED_ERROR));
  }

  @Disabled("WIP - FZ-1640, returns SC_CODE = 200")
  @Tag(REGRESSION)
  @Tag(DISABLED)
  @Tag("FZ-1640")
  @ParameterizedTest(name = "Verify UNSUPPORTED_MEDIA_TYPE response for invalid content type: {0}")
  @Tag(REGRESSION)
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getLeadsExportWithNotSupportedContentType(ContentType contentType)
      throws HttpException {

    var response =
        getExportLeads(
            null,
            null,
            null,
            null,
            null,
            ADMIN_USER,
            CLIENT_AUTOMATION_ID,
            ENDPOINTS_API_KEY,
            contentType,
            true,
            null);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(
      name = "Verify leads export fails with invalid client ID. Client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ArgumentsSource(InvalidArgumentsProvider.class)
  public void getLeadsExportWithInvalidClientId(InvalidArgumentsHolder clientId)
      throws HttpException {
    var response =
        getExportLeads(
            null,
            null,
            null,
            null,
            null,
            ADMIN_USER,
            clientId.getArguments(),
            ENDPOINTS_API_KEY,
            ContentType.JSON,
            true,
            null);

    response
        .then()
        .assertThat()
        .statusCode(in(List.of(HttpStatus.SC_BAD_REQUEST, SC_FORBIDDEN)))
        .body("error.status", anyOf(equalTo(CODE_INVALID_CLIENT), equalTo(CODE_FORBIDDEN)));
  }
}
