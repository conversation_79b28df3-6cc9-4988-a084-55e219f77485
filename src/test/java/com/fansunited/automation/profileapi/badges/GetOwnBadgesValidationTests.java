package com.fansunited.automation.profileapi.badges;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.arguments.predictionapi.InvalidJwtTokenArgumentsProviderUser;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.profileapi.ProfileBadgesEndpoint;
import com.fansunited.automation.core.base.profileapi.BadgesBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Profile Api - GET /v1/profile/badges endpoint validation tests")
public class GetOwnBadgesValidationTests extends BadgesBaseTest {

  @ParameterizedTest(name = "Verify own badges cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getOwnBadgesWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        ProfileBadgesEndpoint.getOwnBadges(getCurrentTestUser().getEmail(), CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting own badges with invalid client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getOwnBadgesWithInvalidClientId(String clientId)
      throws HttpException {

    var response =
        ProfileBadgesEndpoint.getOwnBadges(getCurrentTestUser().getEmail(), clientId,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify own badges cannot be fetched with invalid JWT token. Jwt token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProviderUser.class)
  public void getOwnBadgesWithInvalidJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        ProfileBadgesEndpoint.getOwnBadges(argumentsHolder.getJwtToken(), null, true,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify own badges cannot be fetched without JWT token")
  public void getOwnBadgesWithoutJwtToken() throws HttpException {

    var response =
        ProfileBadgesEndpoint.getOwnBadges(null, null, false, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @Disabled
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting own badges with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1115")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getOwnBadgesWithNonSupportedContentType(ContentType contentType)
      throws HttpException {

    var response =
        ProfileBadgesEndpoint.getOwnBadges(getCurrentTestUser().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify own badges are fetched in JSON format if content type is NOT specified")
  public void getOwnBadgesWithoutSpecifyingContentType() throws HttpException {

    var response =
        ProfileBadgesEndpoint.getOwnBadges(getCurrentTestUser().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
