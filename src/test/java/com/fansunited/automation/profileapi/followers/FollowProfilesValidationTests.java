package com.fansunited.automation.profileapi.followers;

import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FOLLOWERS_COUNT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.FOLLOWING_COUNT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.PROFILE_IDS_PROP;
import static com.fansunited.automation.constants.ApiErrorCodes.ProfileErrorCodes.CODE_INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_PROFILE_ID;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.followProfilesForCurrentTestUser;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.followProfilesForUserWithToken;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.generateFollowingsForCurrentTestUser;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.unfollowProfilesForCurrentTestUser;
import static com.fansunited.automation.core.apis.profileapi.FollowProfileEndpoint.unfollowProfilesForUser;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.deleteUser;
import static com.fansunited.automation.core.apis.profileapi.ProfileFollowingEndpoint.getCurrentTestUserFollowings;
import static com.fansunited.automation.core.apis.profileapi.ProfilesEndpoint.createProfiles;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.ApiErrorCodes;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.profileapi.ProfileEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.json.simple.JSONObject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Profile Api - POST/DELETE /v1/profile/follow endpoint validation tests")
public class FollowProfilesValidationTests extends ProfileApiBaseTest {

  @ParameterizedTest(name = "Verify profiles cannot be followed with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void followProfilesWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of());

    var response =
        followProfilesForCurrentTestUser(body, CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify profiles cannot be followed with invalid JWT token. Token: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  public void followProfilesWithInvalidOrEmptyJwtToken(
      InvalidJwtTokenArgumentsHolder argumentsHolder) throws HttpException {

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of());

    var response = followProfilesForUserWithToken(argumentsHolder.getJwtToken(), body,
        UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify profiles cannot be followed with invalid/missing client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void followProfilesWithInvalidClientId(String clientId) throws HttpException {

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of());

    var response =
        followProfilesForCurrentTestUser(body, clientId,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @ParameterizedTest(name = "Verify API returns empty data when following profile with null or empty id. Profile ID: {arguments}")
  @Tag(REGRESSION)
  @NullAndEmptySource
  public void followProfileWithNullOrEmptyProfileId(String profileId) throws HttpException {

    var profileIds = new ArrayList<String>();
    profileIds.add(profileId);

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, profileIds);

    var postFollowProfileResponse =
        followProfilesForCurrentTestUser(body);

    postFollowProfileResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));

    var getProfileFollowingsResponse = getCurrentTestUserFollowings();

    currentTestResponse.set(getProfileFollowingsResponse);

    getProfileFollowingsResponse
        .then()
        .assertThat()
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify only profiles with valid ids are followed when following profiles with valid and invalid ids")
  public void followProfilesWithValidAndInvalidIds() throws HttpException, IOException,
      FirebaseAuthException, InterruptedException, ExecutionException {

    var profileIdsToFollow = new ArrayList<>(createProfiles(3).keySet());

    profileIdsToFollow.add(UrlParamValues.ProfileApi.INVALID_PROFILE_ID);
    profileIdsToFollow.add("");
    profileIdsToFollow.add(null);

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, profileIdsToFollow);

    var postFollowProfileResponse = followProfilesForCurrentTestUser(body);

    postFollowProfileResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.size()", equalTo(profileIdsToFollow.size() - 3));

    var getProfileFollowingsResponse = getCurrentTestUserFollowings();

    currentTestResponse.set(getProfileFollowingsResponse);

    getProfileFollowingsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.size()", equalTo(profileIdsToFollow.size() - 3));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag("FZ-312")})
  @DisplayName("Verify API returns BAD_REQUEST response when following profiles with empty body")
  public void followProfilesWithEmptyBody() throws HttpException {

    var body = new JSONObject();

    var postFollowProfileResponse = followProfilesForCurrentTestUser(body);

    postFollowProfileResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_NO_PROFILE_IDS));

    var getProfileFollowingsResponse = getCurrentTestUserFollowings();

    currentTestResponse.set(getProfileFollowingsResponse);

    getProfileFollowingsResponse
        .then()
        .assertThat()
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify you cannot follow your own profile")
  public void followYourOwnProfile() throws HttpException {

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of(getCurrentTestUser().getUid()));

    var postFollowProfileResponse = followProfilesForCurrentTestUser(body);

    postFollowProfileResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", is(empty()));

    var getProfileFollowingsResponse = getCurrentTestUserFollowings();

    currentTestResponse.set(getProfileFollowingsResponse);

    getProfileFollowingsResponse
        .then()
        .assertThat()
        .body("data", is(empty()));
  }

  @Test
  @DisplayName("Verify profile cannot be followed with non supported content type")
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  public void followProfileWithNonSupportedContentType()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    var profileIdToFollow = createProfiles(1).keySet().stream().toList().get(0);

    var postFollowProfileResponse =
        followProfilesForCurrentTestUser(profileIdToFollow, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.TEXT);

    postFollowProfileResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(CODE_INVALID_CONTENT_TYPE));

    currentTestResponse.set(postFollowProfileResponse);

    getCurrentTestUserFollowings()
        .then()
        .assertThat()
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns BAD_REQUEST when unfollowing profiles with empty body")
  public void unfollowingProfilesWithEmptyBody()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    var followingIds = generateFollowingsForCurrentTestUser(3);

    var body = new JSONObject();

    var postUnfollowProfileResponse = unfollowProfilesForCurrentTestUser(body);

    postUnfollowProfileResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(ApiErrorCodes.ProfileErrorCodes.CODE_NO_PROFILE_IDS));

    currentTestResponse.set(postUnfollowProfileResponse);

    getCurrentTestUserFollowings()
        .then()
        .assertThat()
        .body("data.size()", equalTo(followingIds.size()));
  }

  @Test
  @Tags({@Tag(REGRESSION),@Tag(DISABLED), @Tag("FZ-766")})
  @DisplayName("Verify profile cannot be unfollowed with non supported content type")
  public void unfollowingProfileWithNonSupportedContentType()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    var followingIds = generateFollowingsForCurrentTestUser(1);

    var body = followingIds.get(0);

    var postUnfollowProfileResponse =
        unfollowProfilesForUser(getCurrentTestUser().getEmail(), body,
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.TEXT);

    postUnfollowProfileResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);

    currentTestResponse.set(postUnfollowProfileResponse);

    getCurrentTestUserFollowings()
        .then()
        .assertThat()
        .body("data.size()", equalTo(followingIds.size()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify profiles only with valid ids are unfollowed when unfollowing profiles with valid and invalid ids")
  public void unfollowingProfilesWithValidAndInvalidIds()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    var followingIds = generateFollowingsForCurrentTestUser(3);

    followingIds.add(UrlParamValues.ProfileApi.INVALID_PROFILE_ID);
    followingIds.add("");
    followingIds.add(null);

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, followingIds);

    var postUnfollowProfileResponse = unfollowProfilesForCurrentTestUser(body);

    postUnfollowProfileResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    currentTestResponse.set(postUnfollowProfileResponse);

    getCurrentTestUserFollowings()
        .then()
        .assertThat()
        .body("data", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify following counter is not decreased when trying to unfollow profile with invalid id")
  public void verifyCounterIsUnchangedWhenTryingToUnfollowProfileWithInvalidId()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    var followingIds = generateFollowingsForCurrentTestUser(3);

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of(INVALID_PROFILE_ID));

    unfollowProfilesForCurrentTestUser(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var response = ProfileEndpoint.getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + FOLLOWING_COUNT, equalTo(followingIds.size()))
        .body("data." + FOLLOWERS_COUNT, equalTo(0));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify counter is not increased upon following profile that's being already followed")
  public void verifyCounterIsUnchangedUponFollowingProfileWithInvalidId()
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    var followingIds = generateFollowingsForCurrentTestUser(3);

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, followingIds);

    followProfilesForCurrentTestUser(body)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var response = ProfileEndpoint.getCurrentTestUserProfileRequest();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data." + FOLLOWING_COUNT, equalTo(followingIds.size()))
        .body("data." + FOLLOWERS_COUNT, equalTo(0));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify deleted profile can not be followed")
  public void followDeletedProfile() throws HttpException, IOException,
      FirebaseAuthException, InterruptedException, ExecutionException {

    var profileToBeDeletedAndFollowed = createUsers(1);

    deleteUser(profileToBeDeletedAndFollowed.get(0).getUid());

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of(profileToBeDeletedAndFollowed.get(0).getUid()));

    var postFollowProfileResponse = followProfilesForCurrentTestUser(body);

    postFollowProfileResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.", is(empty()));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify deleted profile can not be unfollowed")
  public void unfollowDeletedProfile() throws HttpException, IOException,
      FirebaseAuthException, InterruptedException, ExecutionException {

    var profiles = createUsers(2);

    var bodyFollow = new JSONObject();
    bodyFollow.put(PROFILE_IDS_PROP, List.of(profiles.get(0).getUid(), profiles.get(1).getUid()));

    followProfilesForCurrentTestUser(bodyFollow);

    deleteUser(profiles.get(1).getUid());

    var body = new JSONObject();
    body.put(PROFILE_IDS_PROP, List.of(profiles.get(1).getUid()));

    var postUnfollowProfileResponse = unfollowProfilesForCurrentTestUser(body);

    postUnfollowProfileResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(equalTo(""));
  }
}

