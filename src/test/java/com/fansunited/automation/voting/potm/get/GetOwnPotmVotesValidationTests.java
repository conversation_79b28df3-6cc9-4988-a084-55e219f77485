package com.fansunited.automation.voting.potm.get;

import static com.fansunited.automation.constants.ApiErrorCodes.INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.ApiErrorCodes.STATUS_VALIDATION_ERROR;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.potm.GetOwnPotmVotesEndpoint.getOwnVotesForPotm;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidClientIdArguments;
import com.fansunited.automation.arguments.commonarguments.InvalidClientIdHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidContentTypesHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.voting.InvalidVotingContentTypeArguments;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPotmBaseTest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.CoreMatchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Get own votes for 'Player of the match' validation tests GET /v1/football/potm")
public class GetOwnPotmVotesValidationTests extends VotingApiPotmBaseTest {

  @ParameterizedTest(
      name = "Verify getting own Player of the Match votes fails with invalid client ID: {0}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidClientIdArguments.class)
  public void getOwnPotmVotesWithInvalidClientId(InvalidClientIdHolder invalidClientIdHolder)
      throws HttpException {

    getOwnVotesForPotm(
            null,
            null,
            null,
            invalidClientIdHolder.clintId(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            true,
            null)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(invalidClientIdHolder.errorStatus()));
  }

  @ParameterizedTest(
      name =
          "Verify getting own Player of the Match votes fails with missing/non supported Content Type. ContentType: {0}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidVotingContentTypeArguments.class)
  public void getOwnPotmVotesWithInvalidContentType(
      InvalidContentTypesHolder invalidContentTypesHolder) throws HttpException {

    getOwnVotesForPotm(
            null,
            null,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            invalidContentTypesHolder.contentType(),
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            true,
            null)
        .then()
        .assertThat()
        .statusCode(invalidContentTypesHolder.statusCode())
        .body("error.status", CoreMatchers.equalTo(INVALID_CONTENT_TYPE));
  }

  @ParameterizedTest(
      name =
          "Verify getting own Player of the Match votes fails with invalid/missing api key. Api key: {0}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getOwnPotmVotesWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    getOwnVotesForPotm(
            null,
            null,
            null,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            true,
            null)
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name = "Verify getting own Player of the Match votes fails with invalid limit parameter: {0}")
  @Tag(REGRESSION)
  @ValueSource(ints = {-1, 0, 51})
  public void getOwnPotmVotesWithInvalidLimitParameter(int limit) throws HttpException {

    getOwnVotesForPotm(
            limit,
            null,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            getCurrentTestUser().getEmail(),
            true,
            null)
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.status", equalTo(STATUS_VALIDATION_ERROR));
  }
}
