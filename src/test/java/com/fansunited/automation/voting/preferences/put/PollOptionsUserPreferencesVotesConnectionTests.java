package com.fansunited.automation.voting.preferences.put;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.getCurrentTestUserProfileRequest;
import static com.fansunited.automation.core.apis.voting.poll.VoteForAPollEndpoint.voteForeAPoll;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.hasItems;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.voting.entities.PollPreference;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.voting.poll.Poll;
import com.fansunited.automation.model.voting.poll.request.PollRequest;
import com.fansunited.automation.model.voting.poll.request.VotingForPollOptionRequest;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName(
    "Voting API - PUT /v1/polls/{poll_id}/votes endpoint happy path tests - Poll Options User Preferences Connection Tests")
public class PollOptionsUserPreferencesVotesConnectionTests extends VotingApiPollBaseTest {

  private static PollRequest request;
  private static Poll testPoll;

  @BeforeAll
  public static void setUp() throws HttpException {
    request = PollRequest.createPollRequestWithOptionsAndValidPreferences();
    testPoll = createCustomPoll(request).as(Poll.class);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify poll options user preferences connection works")
  public void pollOptionsUserPreferencesConnectionTests() throws HttpException {
    var poll = testPoll.getData();

    // Get the preference IDs from the poll options
    List<String> preferenceIds =
        poll.getOptions().get(0).getPreferencesMapping().stream()
            .map(PollPreference::getPreferenceId)
            .toList();

    var voteRequest =
        VotingForPollOptionRequest.builder()
            .optionIds(List.of(poll.getOptions().get(0).getId()))
            .build();

    var voteRequest1 =
        VotingForPollOptionRequest.builder().optionId(poll.getOptions().get(0).getId()).build();
    // Vote for the poll option
    voteForeAPoll(
            poll.getId(),
            voteRequest1,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail())
        .then()
        .assertThat()
        .statusCode(200);

    // Verify the user preferences are connected to the poll option
    var response = getCurrentTestUserProfileRequest();
    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(200)
        .body("data.preferences.source", everyItem(is(equalTo("poll"))),
            "All preferences should have source 'poll'")
        .body("data.preferences.preference_id", hasItems(preferenceIds.toArray()),
            "Profile should contain expected preference IDs")
        .body("data.preferences.categories", everyItem(hasItems("food", "brand")),
            "All preferences should contain 'food' and 'brand' categories");
  }
}
