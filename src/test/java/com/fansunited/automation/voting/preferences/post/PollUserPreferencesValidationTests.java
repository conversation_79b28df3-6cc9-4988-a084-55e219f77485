package com.fansunited.automation.voting.preferences.post;

import static com.fansunited.automation.constants.ApiErrorCodes.VotingErrorCodes.PREFERENCES_NOT_ENABLED_IN_REQUEST;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.getCurrentTestUserProfileRequest;
import static com.fansunited.automation.core.apis.voting.poll.CreateVotingEndpoint.createPoll;
import static com.fansunited.automation.core.apis.voting.poll.VoteForAPollEndpoint.voteForeAPoll;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.model.voting.poll.request.PollRequest.createPollRequestWithOptionsAndNonExistingPreferences;
import static com.fansunited.automation.model.voting.poll.request.PollRequest.createPollRequestWithOptionsAndValidPreferences;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.voting.poll.Poll;
import com.fansunited.automation.model.voting.poll.request.VotingForPollOptionRequest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName(
    "Voting API - POST /v1/polls/ Create poll endpoint VALIDATION tests - Poll Options User Preferences Connection Tests")
public class PollUserPreferencesValidationTests extends VotingApiPollBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify preferences mapping cannot be set when poll preferences_enabled is false")
  public void verifyPreference1sMappingPreferencesEnabledToFalseTest() throws HttpException {
    var request = createPollRequestWithOptionsAndValidPreferences();
    request.setPreferencesEnabled(false);

    var response =
        createPoll(
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(400)
        .body("error.status", equalTo(PREFERENCES_NOT_ENABLED_IN_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName(
      "Verify voting for non-existing preferences are ignored and not saved to the user's profile")
  public void verifyNonExistingPreferencesAreIgnored() throws HttpException {

    var request = createPollRequestWithOptionsAndNonExistingPreferences();
    var response =
        createPoll(
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);
    response.then().assertThat().statusCode(200);

    var poll = response.as(Poll.class).getData();

    var voteRequest =
        VotingForPollOptionRequest.builder().optionId(poll.getOptions().get(0).getId()).build();

    voteForeAPoll(
            poll.getId(),
            voteRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail())
        .then()
        .assertThat()
        .statusCode(200);

    response = getCurrentTestUserProfileRequest();
    currentTestResponse.set(response);

    response.then().assertThat().statusCode(200).body("data.preferences", is(equalTo(null)));
  }
}
