package com.fansunited.automation.voting.preferences.post;

import static com.fansunited.automation.constants.ApiErrorCodes.VotingErrorCodes.PREFERENCES_NOT_ENABLED_IN_REQUEST;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.CreateVotingEndpoint.createPoll;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.model.voting.poll.request.PollRequest.createPollRequestWithOptionsAndValidPreferences;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;


@DisplayName(
    "Voting API - POST /v1/polls/ Create poll endpoint VALIDATION tests - Poll Options User Preferences Connection Tests")
public class PollUserPreferencesValidationTests extends VotingApiPollBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify preferences mapping cannot be set when poll preferences_enabled is false")
  public void verifyPreference1sMappingPreferencesEnabledToFalseTest() throws HttpException {
    var request = createPollRequestWithOptionsAndValidPreferences();
    request.setPreferencesEnabled(false);

    var response =
        createPoll(
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(400)
        .body("error.status", equalTo(PREFERENCES_NOT_ENABLED_IN_REQUEST));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify voting for non-existing preferences are ignored and not saved to the user's profile")
  public void verifyPreferencesMappingPreferencesEnabledToFalseTest() throws HttpException {
    var request = createPollRequestWithOptionsAndValidPreferences();
    request.setPreferencesEnabled(false);

    var response =
        createPoll(
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(400)
        .body("error.status", equalTo(PREFERENCES_NOT_ENABLED_IN_REQUEST));
  }
}
