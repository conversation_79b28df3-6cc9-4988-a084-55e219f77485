package com.fansunited.automation.model.reportingapi.mock;

import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RegistrationProfile {
  private String id;
  private String name;
  private String email;
  private String avatar;
  private CountryProfile country;
  private String gender;
  private Date birthDate;
  private List<ProfileData.Interest> interests;
  private int followingCount;
  private int followersCount;
  private String createdAt;
  private String updatedAt;
  private int points;
}