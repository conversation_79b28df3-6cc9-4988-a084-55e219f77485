package com.fansunited.automation.model.clientapi.features.response;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Data
@Builder
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class Rewards {
  private boolean enabled;
  private List<Point> points;
  private List<TierReward> tiers;
}
