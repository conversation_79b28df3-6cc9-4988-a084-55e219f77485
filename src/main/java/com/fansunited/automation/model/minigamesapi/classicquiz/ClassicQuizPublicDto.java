package com.fansunited.automation.model.minigamesapi.classicquiz;

import com.fansunited.automation.model.CommonStatus;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class ClassicQuizPublicDto {

  private String id;

  private String title;

  private String description;

  private ImagesDto images;

  private int participationCount;

  private CommonStatus status;

  private List<String> flags;

  private LocalDateTime createdAt;

  private LocalDateTime updatedAt;

  private int maxAttempts;
}
