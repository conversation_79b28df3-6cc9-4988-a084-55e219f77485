package com.fansunited.automation.model.predictionapi.games.response;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.RelatedDto;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.GameTiebreaker;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@SuperBuilder
public class GameInstance {

  private Error error;
  private String id;
  private String title;
  private String description;
  private String type;
  private String status;
  @JsonProperty("predictions_cutoff")
  private String predictionsCutoff;

  private List<GameFixture> fixtures;

  private String created_at;

  private String rules;
  @JsonIgnore
  private List<String> flags;

  @JsonProperty ("schedule_open_at")
  private String scheduleOpenAt;
  @JsonIgnore
  private boolean ignoreRelated;

  private List<RelatedDto> related;

  private String updated_at;
  @JsonIgnore
  private Images images;

  private GameTiebreaker tiebreaker;
  @JsonProperty("participants_count")
  private Integer participantsCount;
  private boolean ignoreExcludedProfileIds;
  @JsonProperty("excluded_profile_ids")
  private List<String> excludedProfileIds;
  private Object match_status;
  private boolean ignoreSystemLastKickoff;
  private ApiConstants.AuthRequirement authRequirement;
  @JsonIgnore
  private String system_last_kickoff;

  private String ad_content;

  private Fields labels;
  private Fields customFields;
  private BrandingDTO branding;


  @JsonAnyGetter
  public Map<String, Object> getAdditionalProperties() {
    var props = new HashMap<String, Object>();
    if (!ignoreExcludedProfileIds) {
      props.put("excluded_profile_ids", excludedProfileIds);
    }
    if (!ignoreRelated) {
      props.put("related", related);
    }
    if (!ignoreSystemLastKickoff) {
      props.put("system_last_kickoff", system_last_kickoff);
    }
    return props;
  }
}
