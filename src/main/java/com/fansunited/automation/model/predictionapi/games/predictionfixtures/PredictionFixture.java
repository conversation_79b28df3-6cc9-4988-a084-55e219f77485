package com.fansunited.automation.model.predictionapi.games.predictionfixtures;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "market")
@JsonSubTypes({
    @JsonSubTypes.Type(name = "FT_1X2", value = FullTimeOneXTwoPredictionFixture.class),
    @JsonSubTypes.Type(name = "HT_1X2", value = HalfTimeOneXTwoPredictionFixture.class),
    @JsonSubTypes.Type(name = "BOTH_TEAMS_SCORE", value = BothTeamsScorePredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_GOALS_0_5", value = OverGoals_0_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_GOALS_1_5", value = OverGoals_1_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_GOALS_2_5", value = OverGoals_2_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_GOALS_3_5", value = OverGoals_3_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_GOALS_4_5", value = OverGoals_4_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_GOALS_5_5", value = OverGoals_5_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_GOALS_6_5", value = OverGoals_6_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_CORNERS_6_5", value = OverCorners_6_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_CORNERS_7_5", value = OverCorners_7_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_CORNERS_8_5", value = OverCorners_8_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_CORNERS_9_5", value = OverCorners_9_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_CORNERS_10_5", value = OverCorners_10_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_CORNERS_11_5", value = OverCorners_11_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_CORNERS_12_5", value = OverCorners_12_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "OVER_CORNERS_13_5", value = OverCorners_13_5_PredictionFixture.class),
    @JsonSubTypes.Type(name = "DOUBLE_CHANCE", value = DoubleChancePredictionFixture.class),
    @JsonSubTypes.Type(name = "HT_FT", value = HalfTimeFullTimePredictionFixture.class),
    @JsonSubTypes.Type(name = "PLAYER_SCORE", value = PlayerScorePredictionFixture.class),
    @JsonSubTypes.Type(name = "PLAYER_SCORE_TWICE", value = PlayerScoreTwicePredictionFixture.class),
    @JsonSubTypes.Type(name = "PLAYER_SCORE_HATTRICK", value = PlayerScoreHattrickPredictionFixture.class),
    @JsonSubTypes.Type(name = "PLAYER_YELLOW_CARD", value = PlayerYellowCardPredictionFixture.class),
    @JsonSubTypes.Type(name = "PLAYER_RED_CARD", value = PlayerRedCardPredictionFixture.class),
    @JsonSubTypes.Type(name = "RED_CARD_MATCH", value = RedCardMatchPredictionFixture.class),
    @JsonSubTypes.Type(name = "PENALTY_MATCH", value = PenaltyMatchPredictionFixture.class),
    @JsonSubTypes.Type(name = "PLAYER_SCORE_FIRST_GOAL", value = PlayerScoreFirstGoalPredictionFixture.class),
    @JsonSubTypes.Type(name = "CORNERS_MATCH", value = CornersMatchPredictionFixture.class),
    @JsonSubTypes.Type(name = "CORRECT_SCORE", value = CorrectScorePredictionFixture.class),
    @JsonSubTypes.Type(name = "CORRECT_SCORE_HT", value = CorrectScoreHTPredictionFixture.class),
    @JsonSubTypes.Type(name = "CORRECT_SCORE_ADVANCED", value = CorrectScoreAdvancedPredictionFixture.class),
})
public class PredictionFixture {

  @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
  private String matchId;
  @JsonIgnore
  private boolean ignoreMatchId;

  @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
  private String matchType;
  @JsonIgnore
  private boolean ignoreMatchType;

  @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
  @EqualsAndHashCode.Exclude
  private PredictionResult result;

  protected Map<String, Object> getProperties() {
    var additionalProperties = new HashMap<String, Object>();
    if (!ignoreMatchId) {
      additionalProperties.put("match_id", matchId);
    }
    if (!ignoreMatchType) {
      additionalProperties.put("match_type", matchType);
    }
    if (result != null) {
      additionalProperties.put("result", result);
    }
    return additionalProperties;
  }
}
