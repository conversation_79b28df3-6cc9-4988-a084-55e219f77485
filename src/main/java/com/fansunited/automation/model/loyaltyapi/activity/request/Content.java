package com.fansunited.automation.model.loyaltyapi.activity.request;

import com.github.javafaker.Faker;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Content {
  private String id;
  private String type;
  private String label;

  public static  Content createContentWithRandomData() {
    Faker faker = new Faker();
    return  Content.builder()
            .id(faker.idNumber().valid())
            .type(faker.file().mimeType())
            .label(faker.lorem().word())
            .build();
  }
}