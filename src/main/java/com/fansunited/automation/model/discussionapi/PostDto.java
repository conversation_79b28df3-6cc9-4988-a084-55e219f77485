package com.fansunited.automation.model.discussionapi;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PostDto {
  private String id;

  private String userId;

  private String discussionId;

  private String content;

  private int repliesCount;

  private String replyId;

  private List<ReactionDto> reactions;

  private int reactionsCount;

  private List<PostVersionDto> versions;

  private boolean deleted;

  private LocalDateTime deletedAt;

  private String deletedBy;

  private boolean moderated;

  private LocalDateTime moderatedAt;

  private String moderatedBy;

  private String moderatedReason;

  private List<PostReportDto> reports;

  private int reportsCount;

  private String createdAt;

  private String updatedAt;

  private boolean privatePost;
  private String anonymous_nickname;
}
