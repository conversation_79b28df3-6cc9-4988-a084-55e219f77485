package com.fansunited.automation.core.base.minigames;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.CreateQuizEndpoint.createQuiz;
import static com.fansunited.automation.core.apis.minigames.eitherOr.CreateEitherOrEndpoint.createEitherOr;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.ApiConstants.ProfileApi.Interest;
import com.fansunited.automation.constants.ApiConstants.ProfileApi.Interest.Football;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.AuthBase;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.model.loyaltyapi.activity.request.Campaign;
import com.fansunited.automation.model.loyaltyapi.activity.request.Content;
import com.fansunited.automation.model.loyaltyapi.activity.request.Context;
import com.fansunited.automation.model.loyaltyapi.activity.request.Tag;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingColorsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingImagesDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingUrlsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.GameImagesDto;
import com.fansunited.automation.model.minigamesapi.classicquiz.ImagesDto;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizOptionsRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizOptionsUpdateRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizParticipateQuestionsRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizParticipateRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizQuestionsRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizQuestionsUpdateRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.request.ClassicQuizUpdateRequest;
import com.fansunited.automation.model.minigamesapi.classicquiz.response.ClassicQuizResponse;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrOption;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrPoint;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrWinningCondition;
import com.fansunited.automation.model.minigamesapi.eitheror.request.EitherOrParticipationRequest;
import com.fansunited.automation.model.minigamesapi.eitheror.request.EitherOrRequest;
import com.fansunited.automation.model.minigamesapi.eitheror.response.EitherOrResponse;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;

public class MiniGamesApiBaseTest extends AuthBase {

  public static String mainImageUrl = "https://example/imageurl.gtr";
  public static String coverImageUrl = "https://example/folder/imageurl.gtr";
  public static String mobileImageUrl = "http://example/imageurl.gtr";
  public static String labels = "Carlsberg 2024";
  public static String labels1 = "Zagorka 2025";

  public static String id = new Faker().idNumber().toString();

  public static int value = new Faker().number().numberBetween(1, 9);

  public static ClassicQuizResponse createQuizForTest(CommonStatus status) throws HttpException {

    var CreateQuizResponse =
        createQuiz(
            classicQuizRequest(status),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    CreateQuizResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    return CreateQuizResponse.as(ClassicQuizResponse.class);
  }

  public static ClassicQuizRequest classicQuizRequest(CommonStatus status) {
    ClassicQuizRequest CreateClassicQuizRequest;

    var tags =
        List.of(
            Tag.builder()
                .source(Interest.FOOTBALL.getSource())
                .type(Interest.FOOTBALL.getSource())
                .id(TEAM_ID_LIVERPOOL)
                .build());

    var contentId = UUID.randomUUID().toString();
    var campaignId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType(new Faker().howIMetYourMother().character());
    content.setLabel(new Faker().howIMetYourMother().character());

    var campaign = new Campaign();
    campaign.setId(campaignId);
    campaign.setLabel(labels);

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);
    context.setCampaign(campaign);

    CreateClassicQuizRequest =
        ClassicQuizRequest.builder()
            .related(null)
            .scored(true)
            .max_attempts(10)
            .points(2)
            .title("The best Classic Quiz")
            .branding(
                BrandingDTO.builder()
                    .colors(
                        BrandingColorsDTO.builder()
                            .additionalColor("test")
                            .backgroundColor("")
                            .primaryColor("")
                            .contentColor("")
                            .borderColor("")
                            .secondaryColor("")
                            .build())
                    .urls(
                        BrandingUrlsDTO.builder()
                            .primaryUrl("тест")
                            .privacyPolicyUrl("")
                            .primaryUrl("/")
                            .additionalUrl("/")
                            .build())
                    .images(
                        BrandingImagesDTO.builder()
                            .additionalImage("/")
                            .backgroundImage("тест")
                            .mainLogo("//jwt.io/")
                            .mobileBackgroundImage("")
                            .build())
                    .build())
            .status(status)
            .description("Only for test")
            .context(context)
            .flags(
                List.of(
                    new Faker().howIMetYourMother().toString(),
                    new Faker().howIMetYourMother().toString()))
            .images(
                ImagesDto.builder()
                    .main(mainImageUrl)
                    .mobile(mobileImageUrl)
                    .cover(coverImageUrl)
                    .build())
            .questions(
                List.of(
                    ClassicQuizQuestionsRequest.builder()
                        .correct(true)
                        .question("How are you today?")
                        .embed_code("test")
                        .options(
                            List.of(
                                ClassicQuizOptionsRequest.builder()
                                    .option("So so!")
                                    .correct(true)
                                    .include_correct_options(true)
                                    .images(
                                        GameImagesDto.builder()
                                            .main(mainImageUrl)
                                            .mobile(mobileImageUrl)
                                            .build())
                                    .build(),
                                ClassicQuizOptionsRequest.builder()
                                    .option("Very good!")
                                    .correct(false)
                                    .images(
                                        GameImagesDto.builder()
                                            .main(mainImageUrl)
                                            .mobile(mobileImageUrl)
                                            .build())
                                    .build()))
                        .build(),
                    ClassicQuizQuestionsRequest.builder()
                        .question("What is the capital of France?")
                        .options(
                            List.of(
                                ClassicQuizOptionsRequest.builder()
                                    .option("Paris!")
                                    .correct(true)
                                    .images(
                                        GameImagesDto.builder()
                                            .main(mainImageUrl)
                                            .mobile(mobileImageUrl)
                                            .build())
                                    .build(),
                                ClassicQuizOptionsRequest.builder()
                                    .option("Berlin!")
                                    .correct(false)
                                    .images(
                                        GameImagesDto.builder()
                                            .main(mainImageUrl)
                                            .mobile(mobileImageUrl)
                                            .build())
                                    .build()))
                        .build()))
            .build();

    return CreateClassicQuizRequest;
  }

  public static ClassicQuizUpdateRequest updateClassicQuizRequest() {
    ClassicQuizUpdateRequest updateCreateClassicQuizRequest;

    var tags =
        List.of(
            Tag.builder()
                .source(Interest.FOOTBALL.getSource())
                .type(Football.TEAM.getType())
                .id(TEAM_ID_LIVERPOOL)
                .build());

    var contentId = UUID.randomUUID().toString();
    var campaignId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType(new Faker().howIMetYourMother().quote());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var campaign = new Campaign();
    campaign.setId(campaignId);
    campaign.setLabel(labels);

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);
    context.setCampaign(campaign);

    updateCreateClassicQuizRequest =
        ClassicQuizUpdateRequest.builder()
            .title("The best Classic Quiz")
            .context(context)
            .status(CommonStatus.INACTIVE)
            .description("Only for tester")
            .flags(List.of("Premier League", "Classic Quiz"))
            .images(
                ImagesDto.builder()
                    .main(mainImageUrl)
                    .mobile(mobileImageUrl)
                    .cover(coverImageUrl)
                    .build())
            .questions(
                List.of(
                    ClassicQuizQuestionsUpdateRequest.builder()
                        .questionId(1)
                        .question("How are you?")
                        .options(
                            List.of(
                                ClassicQuizOptionsUpdateRequest.builder()
                                    .optionId(1)
                                    .option("Sad!")
                                    .correct(true)
                                    .images(
                                        GameImagesDto.builder()
                                            .main(mainImageUrl)
                                            .mobile(mobileImageUrl)
                                            .build())
                                    .build(),
                                ClassicQuizOptionsUpdateRequest.builder()
                                    .optionId(2)
                                    .option("Very good!")
                                    .correct(false)
                                    .images(
                                        (GameImagesDto.builder()
                                            .main(mainImageUrl)
                                            .mobile(mobileImageUrl)
                                            .build()))
                                    .build()))
                        .build()))
            .build();

    return updateCreateClassicQuizRequest;
  }

  public static ClassicQuizParticipateRequest participateClassicQuizRequest(
      Integer questionId, Integer optionId, Integer questionId1, Integer optionId1)
      throws InterruptedException {

    ClassicQuizParticipateRequest participateRequest;
    participateRequest =
        ClassicQuizParticipateRequest.builder()
            .include_correct_options(true)
            .questions(
                List.of(
                    ClassicQuizParticipateQuestionsRequest.builder()
                        .questionId(questionId)
                        .optionId(optionId)
                        .build(),
                    ClassicQuizParticipateQuestionsRequest.builder()
                    .questionId(questionId1)
                    .optionId(optionId1)
                    .build()))
            .build();
 Thread.sleep(1000);
    return participateRequest;
  }

  public static EitherOrRequest createTriviaGameRequest(EitherOrWinningCondition winningCondition) {

    var tags =
        List.of(
            Tag.builder()
                .source(Interest.FOOTBALL.getSource())
                .type(Interest.FOOTBALL.getSource())
                .id(TEAM_ID_LIVERPOOL)
                .build());

    var contentId = UUID.randomUUID().toString();
    var campaignId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType(new Faker().howIMetYourMother().character());
    content.setLabel(new Faker().howIMetYourMother().character());

    var campaign = new Campaign();
    campaign.setId(campaignId);
    campaign.setLabel(labels);

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);
    context.setCampaign(campaign);
    EitherOrRequest createEitherOrRequest;

    createEitherOrRequest =
        EitherOrRequest.builder()
            .title("Only for test")
            .description("Just testing")
            .branding(
                BrandingDTO.builder()
                    .colors(
                        BrandingColorsDTO.builder()
                            .additionalColor("test")
                            .backgroundColor("")
                            .primaryColor("")
                            .contentColor("")
                            .borderColor("")
                            .secondaryColor("")
                            .build())
                    .urls(
                        BrandingUrlsDTO.builder()
                            .primaryUrl("тест")
                            .privacyPolicyUrl("тест")
                            .additionalUrl("тест")
                            .build())
                    .images(
                        BrandingImagesDTO.builder()
                            .additionalImage("тест")
                            .backgroundImage("тест")
                            .mainLogo("тест")
                            .mobileBackgroundImage("тест")
                            .build())
                    .build())
            .context(context)
            .flags(List.of("flag1", "flag2"))
            .images(
                ImagesDto.builder().main(mainImageUrl).mobile(mobileImageUrl).cover("test").build())
            .status(String.valueOf(CommonStatus.ACTIVE))
            .winning_condition(winningCondition)
            .points(
                (List.of(
                    EitherOrPoint.builder().correct_steps(0).score(5).build(),
                    EitherOrPoint.builder().correct_steps(5).score(10).build(),
                    EitherOrPoint.builder().correct_steps(10).score(20).build())))
            .options(
                List.of(
                    (EitherOrOption.builder()
                        .id("1")
                        .label(labels)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .value(1)
                        .build()),
                    (EitherOrOption.builder()
                        .id("2")
                        .label(labels1)
                        .value(2)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("3")
                        .value(3)
                        .label(labels)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("4")
                        .value(4)
                        .label(labels1)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("5")
                        .value(5)
                        .label(labels)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("6")
                        .value(6)
                        .label(labels1)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("7")
                        .value(7)
                        .label(labels)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("8")
                        .value(8)
                        .label(labels1)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("9")
                        .value(9)
                        .label(labels)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("10")
                        .value(10)
                        .label(labels1)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build())))
            .lives(3)
            .rules("no bugs")
            .time(3)
            .build();
    return createEitherOrRequest;
  }

  public static EitherOrRequest updateTriviaGameRequest(
      EitherOrWinningCondition winningCondition, CommonStatus status) {

    EitherOrRequest updateEitherOrRequest;

    updateEitherOrRequest =
        EitherOrRequest.builder()
            .title("Only for test new")
            .description("Just testing new")
            .flags(List.of("flag1", "flag2"))
            .branding(
                BrandingDTO.builder()
                    .images(
                        BrandingImagesDTO.builder()
                            .mobileBackgroundImage("test134")
                            .mainLogo("123")
                            .build())
                    .colors(BrandingColorsDTO.builder().additionalColor("тес").build())
                    .urls(
                        BrandingUrlsDTO.builder().secondaryUrl("123").additionalUrl("123").build())
                    .build())
            .images(
                ImagesDto.builder()
                    .main(mainImageUrl)
                    .mobile(mobileImageUrl)
                    .cover(coverImageUrl)
                    .build())
            .status(String.valueOf(status))
            .winning_condition(winningCondition)
            .branding(BrandingDTO.builder().build())
            .question("How are you?")
            .points(
                (List.of(
                    EitherOrPoint.builder().correct_steps(0).score(5).build(),
                    EitherOrPoint.builder().correct_steps(5).score(10).build(),
                    EitherOrPoint.builder().correct_steps(10).score(20).build())))
            .options(
                List.of(
                    (EitherOrOption.builder()
                        .id(id)
                        .label(labels)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .value(1)
                        .build()),
                    (EitherOrOption.builder()
                        .id("2")
                        .label(labels1)
                        .value(2)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("3")
                        .value(3)
                        .label(labels)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("4")
                        .value(5)
                        .label(labels1)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("5")
                        .value(5)
                        .label(labels)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("6")
                        .value(6)
                        .label(labels1)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("7")
                        .value(7)
                        .label(labels1)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("8")
                        .value(8)
                        .label(labels)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("9")
                        .value(9)
                        .label(labels1)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("10")
                        .value(10)
                        .label(labels)
                        .images(
                            GameImagesDto.builder()
                                .main(mainImageUrl)
                                .mobile(mobileImageUrl)
                                .build())
                        .build())))
            .lives(3)
            .rules("no bugs")
            .time(3)
            .build();
    return updateEitherOrRequest;
  }

  public static EitherOrParticipationRequest eitherOrParticipation(
      String answer, boolean expired, String pair, Float times) {

    EitherOrParticipationRequest eitherOrParticipation;
    eitherOrParticipation =
        EitherOrParticipationRequest.builder()
            .answer(answer)
            .expired(expired)
            .pair(pair)
            .time(times)
            .build();

    return eitherOrParticipation;
  }

  public static EitherOrResponse createTriviaGameForTests(EitherOrWinningCondition winningCondition)
      throws HttpException {

    var EitherOrResponse =
        createEitherOr(
            createTriviaGameRequest(winningCondition),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    EitherOrResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    return EitherOrResponse.as(EitherOrResponse.class);
  }

  public static List<String> createQuizzes(int count) throws HttpException {
    List<String> quizIds = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      quizIds.add(createAndVerifyQuiz());
    }
    return quizIds;
  }

  private static String createAndVerifyQuiz() throws HttpException {
    var quizResponse =
        createQuiz(
            classicQuizRequest(CommonStatus.ACTIVE),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    quizResponse.then().assertThat().statusCode(HttpStatus.SC_OK);
    return quizResponse.body().jsonPath().getString("data.id");

  }
}
