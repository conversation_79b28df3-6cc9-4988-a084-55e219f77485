package com.fansunited.automation.core.resolver.hibernate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@EqualsAndHashCode
@Table(name = "asset")
@Entity
public class Asset {
  @Id
  private String id;

  @Column(name = "entity_id")
  private int entityId;
  private String entity;
  private String name;
  private String type;
}