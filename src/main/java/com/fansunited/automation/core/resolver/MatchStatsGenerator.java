package com.fansunited.automation.core.resolver;

import com.fansunited.automation.core.resolver.hibernate.JsonStatistics;
import com.fansunited.automation.core.resolver.hibernate.MatchStats;
import org.apache.commons.lang3.RandomStringUtils;

public class MatchStatsGenerator {
  private MatchStatsGenerator() {
  }

  public static MatchStats generateEmptyMatchStats(String matchId, boolean isHomeTeam) {

    return MatchStats.builder()
        .id(Long.parseLong(RandomStringUtils.randomNumeric(15)))
        .eventId(matchId)
        .homeTeam(isHomeTeam)
        .statistics(JsonStatistics.builder().build()).build();
  }

  public static MatchStats generateMatchStatsCorners(String matchId, boolean isHomeTeam,
      int corners) {

    return MatchStats.builder()
        .id(Long.parseLong(RandomStringUtils.randomNumeric(15)))
        .eventId(matchId)
        .homeTeam(isHomeTeam)
        .statistics(JsonStatistics.builder().corners((byte) corners).build()).build();
  }

  public static MatchStats generateMatchStatsCorners(String matchId, boolean isHomeTeam,
      String totalCorners, String cornersFh, String cornersSh, String cornersEt) {

    MatchStats matchStats = MatchStats.builder()
        .id(Long.parseLong(RandomStringUtils.randomNumeric(15)))
        .eventId(matchId)
        .homeTeam(isHomeTeam)
        .build();

    if (totalCorners == null) {
      matchStats.setStatistics(
          JsonStatistics.builder()
              .corners_1h((byte) Integer.parseInt(cornersFh))
              .corners_2h((byte) Integer.parseInt(cornersSh))
              .corners_et((byte) Integer.parseInt(cornersEt))
              .build());
    } else if (cornersFh == null || cornersSh == null || cornersEt == null) {
      matchStats.setStatistics(
          JsonStatistics.builder()
              .corners((byte) Integer.parseInt(totalCorners))
              .build());
    } else {
      matchStats.setStatistics(
          JsonStatistics.builder()
              .corners((byte) Integer.parseInt(totalCorners))
              .corners_1h((byte) Integer.parseInt(cornersFh))
              .corners_2h((byte) Integer.parseInt(cornersSh))
              .corners_et((byte) Integer.parseInt(cornersEt))
              .build());
    }
    return matchStats;
  }

  public static MatchStats generateMatchStatsCornersSecondHalf(String matchId, boolean isHomeTeam,
      int corners) {

    return MatchStats.builder()
        .id(Long.parseLong(RandomStringUtils.randomNumeric(15)))
        .eventId(matchId)
        .homeTeam(isHomeTeam)
        .statistics(JsonStatistics.builder().corners_2h((byte) corners).build()).build();
  }

  public static MatchStats generateMatchStatsCornersET(String matchId, boolean isHomeTeam,
      int corners) {

    return MatchStats.builder()
        .id(Long.parseLong(RandomStringUtils.randomNumeric(15)))
        .eventId(matchId)
        .homeTeam(isHomeTeam)
        .statistics(JsonStatistics.builder().corners_et((byte) corners).build()).build();
  }

  public static MatchStats generateMatchStatsCards(String matchId, boolean isHomeTeam,
      int redCards1H, int redCards2H, int yellowCards1H, int yellowCards2H ) {

    return MatchStats.builder()
        .id(Long.parseLong(RandomStringUtils.randomNumeric(15)))
        .eventId(matchId)
        .homeTeam(isHomeTeam)
        .statistics(
            JsonStatistics.builder()
                .red_cards_1h((byte) redCards1H)
                .red_cards_2h((byte) redCards2H)
                .yellow_cards_1h((byte) yellowCards1H)
                .yellow_cards_2h((byte) yellowCards2H).build())
        .build();
  }
}
