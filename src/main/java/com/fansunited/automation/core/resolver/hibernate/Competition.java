package com.fansunited.automation.core.resolver.hibernate;

import com.aventstack.extentreports.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@Getter
@Setter
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "competition")
public class Competition implements BaseEntity, Serializable {

  @Id
  private String id;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "country_id")
  private Country country;

  @Column(columnDefinition = "set")
  private String gender;

  @Column(name = "competition_type")
  @JsonProperty("type")
  private String competitionType;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "asset_id")
  private Asset assets;

  private String name;

  @OneToMany(fetch = FetchType.LAZY)
  @JoinTable(
      name = "competition_team",
      joinColumns = {@JoinColumn(name = "competition_id")},
      inverseJoinColumns = {@JoinColumn(name = "team_id")}
  )
  private List<Team> teams;
}
