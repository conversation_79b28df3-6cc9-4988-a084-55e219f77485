package com.fansunited.automation.core.apis.profileapi;

import static com.fansunited.automation.core.apis.profileapi.BaseSetup.getRequiredRequestSpec;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.QueryableRequestSpecification;
import io.restassured.specification.RequestSpecification;
import io.restassured.specification.SpecificationQuerier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.http.HttpException;

@Data
@Builder
@NoArgsConstructor @AllArgsConstructor
public class DeleteProfileEndpoint {

  private String authToken;
  private boolean isAuthRequired;
  private String email;
  private String lang;
  private String clientId;
  private String userId;
  private String apiKey;
  private ContentType contentType;
  private String userToken;
  private FirebaseHelper.FansUnitedProject project;


  public Response deleteUserProfile() throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(authToken, project, email, clientId, apiKey, contentType);

    QueryableRequestSpecification queryRequest = SpecificationQuerier.query(requestSpecification);
    setUserToken(queryRequest.getAuthenticationScheme().toString());

    return requestSpecification
        .pathParam(UrlParamValues.ProfileApi.PATH_PARAM_USER_ID, userId)
        .when()
        .delete(Endpoints.ProfileApi.PROFILE_BY_ID);
  }
}
