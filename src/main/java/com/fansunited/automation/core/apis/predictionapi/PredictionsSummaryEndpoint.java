package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.PATH_PARAM_MATCH_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import java.util.UUID;
import org.apache.http.HttpException;

public class PredictionsSummaryEndpoint extends BaseSetup {

  public static Response getAggregatedPredictionsResultsForMatch(String matchId)
      throws HttpException {

    return getAggregatedPredictionsResultsForMatch(matchId, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, true);
  }

  public static Response getAggregatedPredictionsResultsForMatch(String matchId, String clientId,
      String apiKey, ContentType contentType, boolean shouldBypassCache) throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(null, null, null, clientId, apiKey, contentType);

    // Assign dummy param to avoid caching, hence tests fails

    if (shouldBypassCache) {
      requestSpecification = requestSpecification.queryParam("dummy",
          UUID.randomUUID().toString().replace("-", ""));
    }

    return requestSpecification
        .pathParam(PATH_PARAM_MATCH_ID, matchId)
        .when()
        .get(Endpoints.PredictionApi.PREDICTIONS_SUMMARY);
  }
}
