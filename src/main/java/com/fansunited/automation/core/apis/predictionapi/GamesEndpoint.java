package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.QUERY_PARAM_SORT_ORDER;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.QUERY_PARAM_GAME_IDS;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.QUERY_PARAM_TYPE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_START_AFTER;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_STATUS;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getMatchesIdListAfterDate;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getSingleMatchIdAfterDate;
import static com.fansunited.automation.core.apis.predictionapi.GameEndpoint.updateGame;
import static com.fansunited.automation.helpers.Helper.generateDateTimeInIsoFormat;
import static com.fansunited.automation.helpers.Helper.generateFutureDate;
import static com.fansunited.automation.validators.PredictionApiValidator.validateGameCreationResponse;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.common.Images;
import com.fansunited.automation.model.footballapi.matches.MatchStatus;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.request.CreateGameRequest;
import com.fansunited.automation.model.predictionapi.games.request.UpdateGameRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.http.HttpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GamesEndpoint extends BaseSetup {
  private static final Logger LOG = LoggerFactory.getLogger(GamesEndpoint.class);

  // GET /v1/games
  public static Response getOrderGamesWithIds(String gameIds, String gameType, String sortedOrder) throws HttpException {

    return getListOfGame(gameIds, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON, null, gameType, -1, null, sortedOrder, false);
  }

  public static Response getGamesList(String gameType) throws HttpException {

    return getListOfGames(CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, null, gameType, -1, null);
  }

  public static Response getGamesWithIds(String gameIds, String gameType) throws HttpException {

    return getListOfGame(gameIds, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON, null, gameType, -1, null, null, false);
  }

  public static Response getGamesWithIdsAndLimit(String gameIds, String gameType, int limit)
      throws HttpException {
    return getListOfGame(gameIds, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON, null, gameType, limit, null, null, false);
  }


  public static Response getListOfOrderGames(String gameType, String gameStatus, String sortOrder)
      throws HttpException {

    return getListOfGame(null, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, gameStatus, gameType, -1, null,
        sortOrder, true);
  }

  public static Response getListOfGame(String gameType, String gameStatus)
      throws HttpException {

    return getListOfGames(CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, gameStatus, gameType, -1, null);
  }

  public static Response getListOfGamesAfter(String gameType, String startAfter)
      throws HttpException {

    return getListOfGames(CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, null, gameType, -1, startAfter);
  }

  public static Response getListOfGames(String clientId, String apiKey, ContentType contentType,
      String status, String type, int limit, String startAfter)
      throws HttpException {

    return getListOfGame(null, clientId, apiKey, contentType, status, type, limit, startAfter,
        null, true);
  }

  public static Response getListOfGame(String gameIds, String clientId,
      String apiKey, ContentType contentType, String status, String
      type, int limit,
      String startAfter, String sortOrder, boolean shouldBypassCache)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, null, null, clientId, apiKey, contentType);

    if (status != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_STATUS, status);
    }

    if (type != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_TYPE, type);
    }

    if (limit != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }
    if (sortOrder != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_SORT_ORDER, sortOrder);
    }

    if (gameIds != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_GAME_IDS, gameIds);
    }

    // Assign dummy param to avoid caching, hence tests fails
    if (shouldBypassCache) {
      requestSpecification = requestSpecification.queryParam("dummy",
          UUID.randomUUID().toString().replace("-", ""));
    }

    return requestSpecification
        .when()
        .log().ifValidationFails()
        .get(Endpoints.PredictionApi.GAMES);
  }

  // POST /v1/games

  public static List<GameFixture> generateValidFixturesForGameType(GameType gameType,
      List<String> matchIdList) {
    var gameFixtureList = new ArrayList<GameFixture>();

    switch (gameType) {
      case TOP_X -> matchIdList.forEach(match ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .market(PredictionMarket.CORRECT_SCORE.getValue())
              .matchId(match)
              .build()));

      case MATCH_QUIZ -> PredictionMarket.getValidMarkets().forEach(market ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .market(market.getValue())
              .matchId(matchIdList.get(0))
              .build()));
    }
    return gameFixtureList;
  }

  public static List<GameFixture> generateValidFixturesForGameType(GameType gameType,
      ZonedDateTime matchesAfterDate) {

    var matchList = MatchGenerator.generateMatchesInDate( gameType == GameType.TOP_X ? 6 : 1, matchesAfterDate);
    Resolver.openMatchesForPredictions(matchList);
    var matchId = matchList.stream().map(Match::getId).toList();

    var gameFixtureList = new ArrayList<GameFixture>();

    switch (gameType) {
      case TOP_X -> matchId.forEach(match ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .match_status(new MatchStatus())
              .market(PredictionMarket.CORRECT_SCORE.getValue())
              .matchId(match)
              .build()));

      case MATCH_QUIZ -> PredictionMarket.getValidMarkets().forEach(market ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .market(market.getValue())
              .match_status(new MatchStatus())
              .matchId(matchId.get(0))
              .build()));
    }
    return gameFixtureList;
  }

  public static Response createMatchQuizGameForMarkets(GameStatus gameStatus,
      List<PredictionMarket> markets)
      throws HttpException, IllegalArgumentException {

    var predictionsCutoff = generateFutureDate(12);

    return createMatchQuizGameForMarkets(getSingleMatchIdAfterDate(
        getCompetitionsWhitelist(GameType.MATCH_QUIZ),
        generateDateTimeInIsoFormat(predictionsCutoff)), gameStatus, markets, predictionsCutoff);
  }

  public static Response createMatchQuizGameForMarkets(String matchId, GameStatus gameStatus,
      List<PredictionMarket> markets, ZonedDateTime predictionsCutoff)
      throws HttpException, IllegalArgumentException {

    var gameFixtureList = new ArrayList<GameFixture>();

    markets.forEach(market ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(market.getValue())
            .match_status(new MatchStatus())
            .matchId(matchId)
            .build()));

    CreateGameRequest createGameRequest = CreateGameRequest.builder()
        .title(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
        .description("Saturday derbies")
        .predictionsCutoff(generateDateTimeInIsoFormat(predictionsCutoff))
        .type(GameType.MATCH_QUIZ.getValue())
        .fixtures(gameFixtureList)
        .customFields(Fields.builder()
            .label2("test").build())
        .labels(Fields.builder()
            .label2("test").build())
        .status(gameStatus.getValue())
        .build();

    var response = createGame(createGameRequest);
    validateGameCreationResponse(GameType.MATCH_QUIZ, response, createGameRequest);

    return response;
  }

  public static Response createGame(List<String> matchIdList, GameType gameType,
      GameStatus gameStatus, ZonedDateTime predictionsCutoff) throws HttpException {

    Response response;

    var gameFixtureList = new ArrayList<GameFixture>();

    if (gameType == GameType.TOP_X) {
      matchIdList.forEach(matchId ->
          gameFixtureList.add(
              GameFixture.builder()
                  .market(PredictionMarket.CORRECT_SCORE.getValue())
                  .matchId(matchId)
                  .match_status(new MatchStatus())
                  .matchType(MatchType.FOOTBALL.getValue())
                  .build()
          )
      );
      var createGameRequest = CreateGameRequest.builder()
          .title(GameType.TOP_X + " " + UUID.randomUUID())
          .description("Saturday derbies")
          .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
          .type(GameType.TOP_X.getValue())
          .labels(Fields.builder()
              .label1("test")
              .build())
          .customFields(Fields.builder()
              .label1("test")
              .build())
          .fixtures(gameFixtureList)
          .status(gameStatus.getValue())
          .build();

      response = createGame(createGameRequest);
      validateGameCreationResponse(GameType.TOP_X, response, createGameRequest);
    } else {
      PredictionMarket.getValidMarkets().forEach(market ->
          gameFixtureList.add(GameFixture
              .builder()
              .matchType(MatchType.FOOTBALL.getValue())
              .market(market.getValue())
              .matchId(matchIdList.get(0))
              .build()));

      var createGameRequest = CreateGameRequest.builder()
          .title(GameType.MATCH_QUIZ + " " + UUID.randomUUID())
          .description("Saturday derbies")
          .labels(Fields.builder()
              .label1("test")
              .build())
          .customFields(Fields.builder()
              .label1("test")
              .build())
          .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
          .type(GameType.MATCH_QUIZ.getValue())
          .fixtures(gameFixtureList)
          .status(gameStatus.getValue())
          .build();

      response = createGame(createGameRequest);
      validateGameCreationResponse(GameType.MATCH_QUIZ, response, createGameRequest);
    }
    return response;
  }

  /**
   * Returns list with created game ids
   *
   * @param gameType
   * @param numberOfGames
   * @return
   * @throws HttpException
   * @throws IllegalArgumentException
   */

  public static List<String> createGames(GameType gameType, int numberOfGames)
      throws HttpException, IllegalArgumentException {

    return createGames(GameStatus.OPEN, gameType, numberOfGames);
  }

  public static List<String> createGamesInTwoMinutes(GameType gameType, int numberOfGames)
      throws HttpException, IllegalArgumentException {

    return createGamesInTwoMinutes(GameStatus.OPEN, gameType, numberOfGames);
  }

  public static List<String> createGamesWithMatchList(GameType gameType, int numberOfGames, List<com.fansunited.automation.core.resolver.hibernate.Match> matchList)
      throws HttpException, IllegalArgumentException {

    return createGamesWithMatchList(GameStatus.OPEN, gameType, numberOfGames, matchList);
  }

  public static List<String> createGamesWithMatchList(GameStatus gameStatus, GameType gameType,
      int numberOfGames, List<com.fansunited.automation.core.resolver.hibernate.Match> matchList)
      throws IllegalArgumentException, HttpException {

    if (gameStatus != GameStatus.OPEN && gameStatus != GameStatus.PENDING) {
      throw new IllegalArgumentException("Games can only be created with OPEN or PENDING status");
    }

    var predictionsCutoff = generateFutureDate(1);
    List<String> gamesIdList = new ArrayList<>();

    if (gameType == GameType.TOP_X) {
      for (int i = 0; i < numberOfGames; i++) {
        var response =
            createGame(matchList.stream()
                    .map(com.fansunited.automation.core.resolver.hibernate.Match::getId)
                    .collect(Collectors.toList()), gameType,
                gameStatus, predictionsCutoff);
        gamesIdList.add(response.then().extract().body().jsonPath().get(ID_PROP));
      }
    } else {
      for (int i = 0; i < numberOfGames; i++) {
        var response =
            createGame(matchList.stream()
                    .map(com.fansunited.automation.core.resolver.hibernate.Match::getId)
                    .collect(Collectors.toList()), gameType,
                gameStatus, predictionsCutoff);
        gamesIdList.add(response.then().extract().body().jsonPath().get(ID_PROP));
      }
    }
    return gamesIdList;
  }

  /**
   * Returns list with created game ids
   *
   * @param gameStatus
   * @param gameType
   * @param numberOfGames
   * @return
   * @throws IllegalArgumentException
   * @throws HttpException
   */

  public static List<String> createGames(GameStatus gameStatus, GameType gameType,
      int numberOfGames)
      throws IllegalArgumentException, HttpException {
    return createGamesInTwoMinutes(gameStatus, gameType, numberOfGames);
  }

  public static List<String> createGamesInTwoMinutes(GameStatus gameStatus, GameType gameType,
      int numberOfGames)
      throws IllegalArgumentException, HttpException {

    if (gameStatus != GameStatus.OPEN && gameStatus != GameStatus.PENDING) {
      throw new IllegalArgumentException("Games can only be created with OPEN or PENDING status");
    }

    var predictionsCutoff = ZonedDateTime.now().plusMinutes(2);
    var matchList = MatchGenerator.generateMatches(6, false);
    Resolver.openMatchesForPredictions(matchList);

    List<String> gamesIdList = new ArrayList<>();

    if (gameType == GameType.TOP_X) {
      for (int i = 0; i < numberOfGames; i++) {
        var response =
            createGame(matchList.stream().map(Match::getId).collect(Collectors.toList()),
                gameType, gameStatus, predictionsCutoff);
        if (response.getStatusCode() != 200) {
          LOG.error("Failed to create game: ", response.prettyPrint());
        }
        gamesIdList.add(response.then().extract().body().jsonPath().get(ID_PROP));
      }
    } else {
      for (int i = 0; i < numberOfGames; i++) {
        var response =
            createGame(matchList.stream().map(Match::getId).collect(Collectors.toList()), gameType,
                gameStatus, predictionsCutoff);
        gamesIdList.add(response.then().extract().body().jsonPath().get(ID_PROP));
      }
    }
    return gamesIdList;
  }

  public static List<String> createGamesInTwoHours(GameStatus gameStatus, GameType gameType,
      int numberOfGames)
      throws IllegalArgumentException, HttpException {

    if (gameStatus != GameStatus.OPEN && gameStatus != GameStatus.PENDING) {
      throw new IllegalArgumentException("Games can only be created with OPEN or PENDING status");
    }

    var predictionsCutoff = ZonedDateTime.now().plusHours(2);
    var matchList = MatchGenerator.generateMatchesInFuture(6, 3);
    Resolver.openMatchesForPredictions(matchList);


    List<String> gamesIdList = new ArrayList<>();

    if (gameType == GameType.TOP_X) {
      for (int i = 0; i < numberOfGames; i++) {
        var response =
            createGame(matchList.stream().map(Match::getId).collect(Collectors.toList()),
                gameType, gameStatus, predictionsCutoff);
        gamesIdList.add(response.then().extract().body().jsonPath().get(ID_PROP));
      }
    } else {
      for (int i = 0; i < numberOfGames; i++) {
        var response =
            createGame(matchList.stream().map(Match::getId).collect(Collectors.toList()), gameType,
                gameStatus, predictionsCutoff);
        gamesIdList.add(response.then().extract().body().jsonPath().get(ID_PROP));
      }
    }
    return gamesIdList;
  }

  public static Response createGame(Object body) throws HttpException {
    return createGame(body, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, ADMIN_USER,
        CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response createGame(Object body, String authToken) throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(authToken, null, null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    return requestSpecification
        .body(body)
        .when()
        .log().ifValidationFails()
        .post(Endpoints.PredictionApi.GAMES);
  }

  public static Response createGame(Object body,
      FirebaseHelper.FansUnitedProject tokenForProject, String email, String clientId,
      String apiKey,
      ContentType contentType) throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(null, tokenForProject, email, clientId, apiKey, contentType);

    return requestSpecification
        .body(body)
        .when()
        .log().ifValidationFails()
        .post(Endpoints.PredictionApi.GAMES);
  }

  /**
   * This method creates Top X Game by allowing the fixture market to be specified in the CreateGameRequest.
   * This allows flexibility when creating Top X Games.
   *
   * @param createGameRequest the CreateGameRequest which is used as a request body for creating the game
   * @return create Game Response
   * @throws HttpException
   */
  public static Response createTopXGameForSpecificMarket(CreateGameRequest createGameRequest)
      throws HttpException {

    Response response = createGame(createGameRequest);

    validateGameCreationResponse(GameType.TOP_X, response, createGameRequest);

    return response;
  }

  /**
   * The method creates a Game Request Body for Top X Games, which to be used in createGame(Object body) method.
   *
   * @param predictionsCutoff the cutoff time for the match creation
   * @param market            the market for the TopX fixtures
   * @return CreateGameRequest
   * @throws HttpException
   */
  public static CreateGameRequest createGameRequestForTopXGame(ZonedDateTime predictionsCutoff,
      PredictionMarket market, List<String> matchIdList) throws HttpException {

    var gameType = GameType.TOP_X;

    var gameFixtureList = new ArrayList<GameFixture>();

    if (matchIdList == null) {
      matchIdList = getMatchesIdListAfterDate(
          getCompetitionsWhitelist(gameType),
          generateDateTimeInIsoFormat(predictionsCutoff), 6);
    }

    matchIdList.forEach(match ->
        gameFixtureList.add(GameFixture
            .builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .market(market.getValue())
            .matchId(match)
            .build()));

    return CreateGameRequest.builder()
        .title(gameType + " " + UUID.randomUUID())
        .description(gameType + " " + UUID.randomUUID())
        .type(gameType.getValue())
        .predictionsCutoff(Helper.generateDateTimeInIsoFormat(predictionsCutoff))
        .fixtures(gameFixtureList)
        .status(GameStatus.PENDING.getValue())
        .build();
  }

  public static Images setUpImages(String main, String cover, String mobile) {

    return Images.builder()
        .main(main)
        .cover(cover)
        .mobile(mobile)
        .build();
  }

  /**
   * The method is used to update Game Status in Firebase.
   * The method works only with the following game statuses: PENDING, OPEN, CANCELED
   * New Status PENDING	OPEN	LIVE	CLOSED	SETTLED	CANCELED
   * Current Status
   * PENDING	      OK  	  OK	  error	error	  error	    OK
   * OPEN	          error	  OK	  error	error	  error	    OK
   * LIVE	          error	  error	OK	  error	  error	    OK
   * CLOSED	        error	  error	error	OK	    error	    error
   * SETTLED	      error	  error	error	error	  OK	      error
   * CANCELED	      error	  error	error	error	  error	    OK
   *
   * @param status the status that the game will be updated to
   * @param gameInstance the instance of the game that will have updated status
   * @return response of the update
   * @throws HttpException
   */
  public static Response updateGameStatusInFirebase(GameStatus status, GameInstance gameInstance)
      throws HttpException {

    Response response = null;
    if (canStatusBeUpdated(status, gameInstance)) {

      var updatedGameRequest = UpdateGameRequest.builder()
          .status(status.getValue())
          .build();

      response = updateGame(gameInstance.getId(), updatedGameRequest);
    }

    return response;
  }

  private static boolean canStatusBeUpdated(GameStatus status, GameInstance gameInstance) {
    boolean isStatusUpdateAllowed = true;
    switch (gameInstance.getStatus()) {
      case "PENDING":
        if (GameStatus.PENDING.equals(status)) {
          isStatusUpdateAllowed = true;
        } else if (GameStatus.OPEN.equals(status)) {
          isStatusUpdateAllowed = true;
        } else if (GameStatus.LIVE.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.CLOSED.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.SETTLED.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.CANCELED.equals(status)) {
          isStatusUpdateAllowed = true;
        }
        break;
      case "OPEN":
        if (GameStatus.PENDING.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.OPEN.equals(status)) {
          isStatusUpdateAllowed = true;
        } else if (GameStatus.LIVE.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.CLOSED.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.SETTLED.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.CANCELED.equals(status)) {
          isStatusUpdateAllowed = true;
        }
        break;
      case "LIVE":
        if (GameStatus.PENDING.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.OPEN.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.LIVE.equals(status)) {
          isStatusUpdateAllowed = true;
        } else if (GameStatus.CLOSED.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.SETTLED.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.CANCELED.equals(status)) {
          isStatusUpdateAllowed = true;
        }
        break;
      case "CLOSED":
        if (GameStatus.PENDING.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.OPEN.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.LIVE.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.CLOSED.equals(status)) {
          isStatusUpdateAllowed = true;
        } else if (GameStatus.SETTLED.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.CANCELED.equals(status)) {
          isStatusUpdateAllowed = false;
        }
        break;
      case "SETTLED":
        if (GameStatus.PENDING.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.OPEN.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.LIVE.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.CLOSED.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.SETTLED.equals(status)) {
          isStatusUpdateAllowed = true;
        } else if (GameStatus.CANCELED.equals(status)) {
          isStatusUpdateAllowed = false;
        }
        break;
      case "CANCELED":
        if (GameStatus.PENDING.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.OPEN.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.LIVE.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.CLOSED.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.SETTLED.equals(status)) {
          isStatusUpdateAllowed = false;
        } else if (GameStatus.CANCELED.equals(status)) {
          isStatusUpdateAllowed = true;
        }
        break;
      default:
        throw new IllegalArgumentException("Status not valid");
    }
    return isStatusUpdateAllowed;
  }
}
