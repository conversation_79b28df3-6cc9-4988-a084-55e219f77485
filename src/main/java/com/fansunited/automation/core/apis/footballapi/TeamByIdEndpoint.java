package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.SQUAD_PROP;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.VALID_PLAYER_ID;
import static com.fansunited.automation.constants.UrlParamValues.FootballApi.PATH_PARAM_TEAM_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.model.footballapi.teams.Team;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.Map;
import java.util.UUID;

public class TeamByIdEndpoint extends BaseSetup {

  public static Team getTeamDto(String teamId) {
    return getTeamById(teamId).then().extract().body().jsonPath().getObject("data", Team.class);
  }

  public static String getRandomPlayerFromTeam(String teamId) {
    var players = getTeamById(teamId)
        .then()
        .extract()
        .body()
        .jsonPath()
        .getList("data." + SQUAD_PROP + "." + ID_PROP).stream()
        .map(Object::toString).toList();

    try {
      return players.get(generateRandomNumber(0, players.size() - 1));
    } catch (IllegalArgumentException e) {
      // Squad was empty return dummy player id
      return VALID_PLAYER_ID;
    }
  }

  public static String getRandomPlayerFromTeamByPosition(String teamId, String position) {
    var players = getTeamById(teamId)
            .then()
            .extract()
            .body()
            .jsonPath()
            .getList("data." + SQUAD_PROP, Map.class).stream()
            .filter(player -> position.equals((player).get("position")))
            .map(player -> player.get("id").toString())
            .toList();
    try {
      return players.get(generateRandomNumber(0, players.size() - 1));
    } catch (IllegalArgumentException e) {
      // Squad was empty return dummy player id
      return VALID_PLAYER_ID;
    }
  }

  public static Response getTeamById(String teamId) {

    return getTeamById(teamId, UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response getTeamById(String teamId, String lang,
      String apiKey, ContentType contentType) {

    var requestSpecification =
        getRequiredRequestSpec(lang, CLIENT_AUTOMATION_ID, apiKey, contentType);

    // Avoid cache
    requestSpecification =
        requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    return requestSpecification
        .pathParam(PATH_PARAM_TEAM_ID, teamId)
        .queryParam(QUERY_PARAM_CLIENT_ID, CLIENT_AUTOMATION_ID)
        .when()
        .get(Endpoints.FootballApi.TEAM_BY_ID);
  }
}
