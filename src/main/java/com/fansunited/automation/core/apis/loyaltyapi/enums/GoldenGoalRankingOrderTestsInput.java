package com.fansunited.automation.core.apis.loyaltyapi.enums;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum GoldenGoalRankingOrderTestsInput {
  FG1min_P1_0min30p_P2_2min30p_P3_3min30p(
      "1",
      0, 2, 3,
      30, 30, 30,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.THIRD_POSITION.getValue())),

  FG1min_P1_1min30p_P2_1min30p_P3_3min30p(
      "1",
      1, 1, 3,
      30, 30, 30,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.THIRD_POSITION.getValue())),

      FG1min_P1_1min30p_P2_2min30p_P3_3min30p(
      "1",
      1, 2, 3,
      30, 30, 30,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(), PositionsOrder.THIRD_POSITION.getValue())),

  FG1min_P1_1min30p_P2_2min30p_P3_2min30p(
    "1",
    1, 2, 2,
    30, 30, 30,
    List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue())),

  FG40min_P1_67min30p_P2_12min30p_P3_68min30p(
      "40",
      67, 12, 68,
      30, 30, 30,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue())),

  FG40min_P1_12min40p_P2_36min30p_P3_45min30p(
      "40",
      12, 36, 45,
      40, 30, 30,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(), PositionsOrder.THIRD_POSITION.getValue())),

  FG90min_P1_90min30p_P2_80min30p_P3_10min30p(
      "90",
      90, 80, 10,
      30, 30, 30,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(), PositionsOrder.THIRD_POSITION.getValue())),

  FGnull_P1_10min30p_P2_80min30p_P3_90min30p(
      null,
      10, 80, 90,
      30, 30, 30,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.FIRST_POSITION.getValue())),

  FGnull_P1_10min40p_P2_80min30p_P3_90min30p(
      null,
      10, 80, 90,
      40, 30, 30,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue())),

  FGnull_P1_10min40p_P2_80min30p_P3_90min20p(
      null,
          10, 80, 90,
          40, 30, 20,
      List.of(PositionsOrder.FIRST_POSITION.getValue(), PositionsOrder.SECOND_POSITION.getValue(), PositionsOrder.THIRD_POSITION.getValue()));

  private String firstGoal;
  private int goldenGoalPredictionPlayer1;
  private int goldenGoalPredictionPlayer2;
  private int goldenGoalPredictionPlayer3;
  private int pointsPlayer1;
  private int pointsPlayer2;
  private int pointsPlayer3;
  private List<Integer> leaderboardPositionsOrder;

  @AllArgsConstructor
  @Getter
  public enum PositionsOrder {
    FIRST_POSITION(1),
    SECOND_POSITION(2),
    THIRD_POSITION(3);

    private int value;
  }
}
