package com.fansunited.automation.core.apis.loyaltyapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.PLATFORM_OPERATOR;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.PATH_PARAM_USER_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class BadgesEndpoint extends BaseSetup {

  public static Response getBadgesForUser(String userId)
      throws HttpException {
    return getBadgesForUser(userId, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response getBadgesForUser(String userId, String clientId, String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
                true, PLATFORM_OPERATOR, clientId, apiKey, contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_USER_ID, userId)
        .when()
        .get(Endpoints.LoyaltyApi.BADGES_BY_USER_ID);
  }
}
