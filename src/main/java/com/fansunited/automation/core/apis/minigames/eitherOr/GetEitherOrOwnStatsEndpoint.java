package com.fansunited.automation.core.apis.minigames.eitherOr;

import static com.fansunited.automation.constants.UrlParamValues.MiniGamesApi.EITHER_OR_ID;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetEitherOrOwnStatsEndpoint extends EitherOrBaseSetup {

  public static Response getEitherOrOwnStats(String id,String clientId,
      String apiKey, ContentType contentType, FirebaseHelper.FansUnitedProject project,
      String email)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParams(EITHER_OR_ID,id)
        .when()
        .get(Endpoints.MiniGamesApi.EITHER_OR_OWN_STATS);
  }
}
