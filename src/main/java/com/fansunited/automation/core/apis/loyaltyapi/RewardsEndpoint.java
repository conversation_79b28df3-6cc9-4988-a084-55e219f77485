package com.fansunited.automation.core.apis.loyaltyapi;

import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.QUERY_PARAM_TYPE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.QUERY_PARAM_PROFILE_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class RewardsEndpoint extends BaseSetup {

  public static Response setRewardsEndpoint(Object body, String profile_id,
      FirebaseHelper.FansUnitedProject project, String clientId, String apiKey, String type,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, null, clientId, apiKey,
            contentType);

    return requestSpecification
        .body(body)
        .queryParam(QUERY_PARAM_PROFILE_ID, profile_id)
        .queryParam(QUERY_PARAM_TYPE, type)
        .queryParam(QUERY_PARAM_KEY,apiKey)
        .when()
        .post(Endpoints.LoyaltyApi.REWARDS);
  }
}
