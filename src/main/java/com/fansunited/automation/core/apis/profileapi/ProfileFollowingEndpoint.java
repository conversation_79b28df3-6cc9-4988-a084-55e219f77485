package com.fansunited.automation.core.apis.profileapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.base.AuthBase.getCurrentTestUser;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ProfileFollowingEndpoint extends BaseSetup {

  public static Response getCurrentTestUserFollowings() throws HttpException {

    return getUserFollowings(getCurrentTestUser().getEmail());
  }

  public static Response getCurrentTestUserFollowings(String clientId, String apiKey,
      ContentType contentType, int limit, String startAfter) throws HttpException {

    return getUserFollowings(getCurrentTestUser().getEmail(), clientId, apiKey, contentType, limit,
        startAfter);
  }

  public static Response getUserFollowings(String email) throws HttpException {

    return getUserFollowings(email, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON, -1, null);
  }

  public static Response getUserFollowingsWithToken(String authToken, String clientId,
      String apiKey,
      ContentType contentType, int limit, String startAfter) throws HttpException {

    var requestSpecification = getRequiredRequestSpec(authToken, true, null,
        null, clientId,
        apiKey, contentType);

    if (limit != -1) {
      requestSpecification =
          requestSpecification.queryParam(UrlParamValues.QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification =
          requestSpecification.queryParam(UrlParamValues.QUERY_PARAM_START_AFTER,
              startAfter);
    }

    return requestSpecification
        .when()
        .get(Endpoints.ProfileApi.PROFILE_FOLLOWINGS);
  }

  public static Response getUserFollowings(String email, String clientId, String apiKey,
      ContentType contentType, int limit, String startAfter) throws HttpException {

    var requestSpecification = getRequiredRequestSpec(null, true, email,
        null, clientId,
        apiKey, contentType);

    if (limit != -1) {
      requestSpecification =
          requestSpecification.queryParam(UrlParamValues.QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification =
          requestSpecification.queryParam(UrlParamValues.QUERY_PARAM_START_AFTER,
              startAfter);
    }

    return requestSpecification
        .when()
        .get(Endpoints.ProfileApi.PROFILE_FOLLOWINGS);
  }
}
