package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import io.restassured.http.ContentType;
import io.restassured.response.Response;

public class OptionsFootballEndpoint extends BaseSetup {

  public static Response optionsFootballApi(String endpoint) {

    var requestSpecification =
        getRequiredRequestSpec(
            UrlParamValues.Language.EN.getValue(),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    return requestSpecification.when().options(endpoint);
  }
}
