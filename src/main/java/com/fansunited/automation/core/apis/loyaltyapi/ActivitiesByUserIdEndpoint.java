package com.fansunited.automation.core.apis.loyaltyapi;

import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.PATH_PARAM_USER_ID;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.QUERY_PARAM_ACTION;
import static com.fansunited.automation.constants.UrlParamValues.LoyaltyApi.QUERY_PARAM_PAGE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LIMIT;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.HttpValues;
import io.restassured.http.ContentType;
import io.restassured.http.Header;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ActivitiesByUserIdEndpoint extends BaseSetup {

  public static Response getActivitiesForUser(String userId, String filterAction)
      throws HttpException {
    return getActivitiesForUser(userId, filterAction, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response getActivitiesForUserWithOriginHeader(String origin, String userId)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, null,
            false, null, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    return requestSpecification
        .header(new Header(HttpValues.HeaderKey.ORIGIN, origin))
        .pathParam(PATH_PARAM_USER_ID, userId)
        .when()
        .get(Endpoints.LoyaltyApi.ACTIVITIES_BY_USER_ID);
  }

  public static Response getActivitiesForUser(String userId, String filterAction,
      String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, null,
            false, null, clientId, apiKey, contentType);

    if (filterAction != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_ACTION, filterAction);
    }
    if (filterAction != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }
    return requestSpecification
        .pathParam(PATH_PARAM_USER_ID, userId)
        .queryParam(  QUERY_PARAM_KEY,apiKey)
        .when()
        .get(Endpoints.LoyaltyApi.ACTIVITIES_BY_USER_ID);
  }

  public static Response getActivitiesForUser(String userId, String filterAction, int pageNumber, String limit)
      throws HttpException {
    return getActivitiesForUser(userId, filterAction, pageNumber, limit, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response getActivitiesForUser(String userId, String filterAction, int pageNumber,
      String limit, String clientId, String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, null,
            false, null, clientId, apiKey, contentType);

      if (pageNumber > 0) {
        requestSpecification = requestSpecification.queryParam(QUERY_PARAM_PAGE, pageNumber);
      } else {
        throw new RuntimeException("page: Page cannot be zero or negative number.");
      }

    if (limit != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (filterAction != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_ACTION, filterAction);
    }

    return requestSpecification
        .pathParam(PATH_PARAM_USER_ID, userId)
        .when()
        .get(Endpoints.LoyaltyApi.ACTIVITIES_BY_USER_ID);
  }
}
