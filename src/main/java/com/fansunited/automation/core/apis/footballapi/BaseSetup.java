package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LANG;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;

import com.fansunited.automation.helpers.ConfigReader;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;

class BaseSetup {

  private static final String baseUri =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.FOOTBALL_API_BASE_URL);
  private static final String port =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.FOOTBALL_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec(String lang,
      String clientId, String apiKey, ContentType contentType) {

    RequestSpecification requestSpecification = requestWithoutAuth(baseUri, port);

    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }

    if (lang != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LANG, lang);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }

    return requestSpecification;
  }
}
