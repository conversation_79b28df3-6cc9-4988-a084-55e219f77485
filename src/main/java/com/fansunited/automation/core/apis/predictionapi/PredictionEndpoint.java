package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.PATH_PARAM_PREDICTION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.base.AuthBase.getCurrentTestUser;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class PredictionEndpoint extends BaseSetup {

  public static Response deletePrediction(String predictionId) throws HttpException {

    return deletePrediction(predictionId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response deletePredictionUsingUser(String predictionId, String email)
      throws HttpException {

    return deletePrediction(predictionId, null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, email, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response deletePredictionUsingStaffUser(String predictionId, String email)
      throws HttpException {

    return deletePrediction(predictionId, null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, email, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response deletePrediction(String predictionId, String clientId, String apiKey,
      ContentType contentType) throws HttpException {

    return deletePrediction(predictionId, null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, getCurrentTestUser().getEmail(),
        clientId, apiKey, contentType);
  }

  public static Response deletePrediction(String predictionId, String authToken)
      throws HttpException {

    return deletePrediction(predictionId, authToken, null, null, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response deletePrediction(String predictionId, String authToken,
      FirebaseHelper.FansUnitedProject project, String email, String clientId,
      String apiKey,
      ContentType contentType) throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project, email, clientId, apiKey, contentType);

    if (predictionId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_PREDICTION_ID, predictionId);
    }

    return requestSpecification
        .when()
        .delete(Endpoints.PredictionApi.PREDICTION_BY_ID);
  }
}
