package com.fansunited.automation.core.apis.voting.entities;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint.getClientsByIdFeatures;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.model.clientapi.features.response.FeaturesResponse;
import com.fansunited.automation.model.clientapi.features.response.ProfilePreference;
import com.fansunited.automation.model.common.Images;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class PollOption {
  private String id;
  private String title;
  private String description;
  private Images images;
  private int votes;
  private List<PollPreference> preferencesMapping;

  @JsonProperty("embed_code")
  private String embedCode;

  public static PollOption createOptionWithRandomData() {
    Faker faker = new Faker();
    String htmlCode =
        """
          <!DOCTYPE html>
          <html>
          <head>
            <title>Button Example</title>
          </head>
          <body>
           <p>%s</p>
            <button id="testButton" onclick="alert('Button clicked!')">click test</button>
          </body>
          </html>
         """
            .formatted(faker.chuckNorris().fact());

    return PollOption.builder()
        .title(faker.lorem().word())
        .description(faker.lorem().sentence())
        .images(Images.createImagesWithRandomData())
        .embedCode(htmlCode)
        .build();
  }

  public static PollOption createOptionWithPreferencesAndRandomData() {
    Faker faker = new Faker();
    var pollOption = createOptionWithRandomData();
    pollOption.setPreferencesMapping(
        List.of(
            new PollPreference(faker.company().name()),
            new PollPreference(faker.company().name())));
    return pollOption;
  }

  public static PollOption createOptionWithValidPreferences() {
    FeaturesResponse features;
    // We need to get client's features to extract the valid preferences ids
    try {
      features =
          getClientsByIdFeatures(
                  CLIENT_AUTOMATION_ID,
                  AuthConstants.ENDPOINTS_API_KEY,
                  ContentType.JSON,
                  FANS_UNITED_CLIENTS)
              .as(FeaturesResponse.class);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    var preferencesIds =
        features.getData().getProfilePreferences().getPreferences().stream()
            .map(ProfilePreference::getId)
            .toList();

    var pollOption = createOptionWithRandomData();

    // Create preferences mapping with first 2 preference IDs (if available)
    List<PollPreference> preferencesMapping = new ArrayList<>();
    if (!preferencesIds.isEmpty()) {
      preferencesMapping.add(new PollPreference(preferencesIds.get(0)));
      if (preferencesIds.size() > 1) {
        preferencesMapping.add(new PollPreference(preferencesIds.get(1)));
      }
    }

    pollOption.setPreferencesMapping(preferencesMapping);
    return pollOption;
  }
}
