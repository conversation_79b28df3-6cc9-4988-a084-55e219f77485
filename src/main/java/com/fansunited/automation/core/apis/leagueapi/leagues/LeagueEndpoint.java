package com.fansunited.automation.core.apis.leagueapi.leagues;

import static com.fansunited.automation.constants.UrlParamValues.LeaguesApi.PATH_PARAM_LEAGUE_ID;
import static com.fansunited.automation.constants.UrlParamValues.LeaguesApi.QUERY_PARAM_TEMPLATE_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.leagueapi.BaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class LeagueEndpoint extends BaseSetup {

  public static Response updateLeagueTemplate(
      String leagueId, Object body, String email) throws HttpException {
    return updateLeagueTemplate(
        leagueId,
        body,
        email,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, null,
        ContentType.JSON);
  }

  public static Response updateLeagueTemplate(
      String leagueId,
      Object body,
      String email,
      FirebaseHelper.FansUnitedProject project,
      String clientId,
      String apiKey,
      String authToken,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(project, apiKey, clientId, contentType, authToken, email);
    return requestSpecification
        .pathParam(PATH_PARAM_LEAGUE_ID, leagueId)
        .body(body)
        .when()
        .post(Endpoints.LeaguesApi.UPDATE_LEAGUE_TEMPLATE_BY_ID);
  }

  public static Response updateLeague(String leagueId, Object body, String email)
      throws HttpException {
    return updateLeague(
        leagueId,
        body,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        email,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  private static Response updateLeague(
      String leagueId,
      Object body,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(project, apiKey, clientId, contentType, null, email);
    return requestSpecification
        .pathParam(PATH_PARAM_LEAGUE_ID, leagueId)
        .body(body)
        .when()
        .put(Endpoints.LeaguesApi.UPDATE_LEAGUE_BY_ID);
  }

  public static Response getLeagueById(String leagueId, String email) throws HttpException {

    return getLeagueById(
        leagueId, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, email);
  }

  public static Response getLeagueById(
      String leagueId, String clientId, String apiKey, ContentType contentType, String email)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            apiKey,
            clientId,
            contentType,
            null,
            email);

    return requestSpecification
        .pathParam(PATH_PARAM_LEAGUE_ID, leagueId)
        .when()
        .get(Endpoints.LeaguesApi.GET_LEAGUE_BY_ID);
  }


  public static Response getPredictionsForLeagues(
      String leagueId,String templateId,String clientId, String apiKey, ContentType contentType, String email)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            apiKey,
            clientId,
            contentType,
            null,
            email);

    return requestSpecification
        .pathParam(PATH_PARAM_LEAGUE_ID, leagueId)
        .queryParam(QUERY_PARAM_TEMPLATE_ID,templateId)
        .when()
        .get(Endpoints.LeaguesApi.GET_PREDICTIONS_FOR_LEAGUE);
  }
}
