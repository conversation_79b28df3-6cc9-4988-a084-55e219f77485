package com.fansunited.automation.core.apis.voting.potm;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.PATH_PARAM_MATCH_IDS;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.VotingApi.QUERY_PARAM_START_AFTER;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints.VotingApi;
import com.fansunited.automation.core.apis.voting.VotingBaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.List;
import java.util.UUID;
import org.apache.http.HttpException;

public class GetOwnPotmVotesEndpoint extends VotingBaseSetup {
  public static Response getOwnVotesForPotm(
      Integer limit,
      List<String> matchIds,
      String startAfter,
      String clientId,
      String apiKey,
      ContentType contentType,
      FirebaseHelper.FansUnitedProject project,
      String email,
      boolean isAuthRequired,
      String authToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            authToken, project, isAuthRequired, email, clientId, apiKey, contentType);

    if (limit != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (matchIds != null && !matchIds.isEmpty()) {
      String formattedMatchIds = String.join(",", matchIds);
      requestSpecification =
          requestSpecification.queryParam(PATH_PARAM_MATCH_IDS, formattedMatchIds);
    }
    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    return requestSpecification
        .queryParam("dummy", UUID.randomUUID().toString().replace("-", ""))
        .when()
        .get(VotingApi.GET_OWN_POTM_VOTES);
  }

  public static Response getOwnVotesForPotm(
      Integer limit, List<String> matchIds, String startAfter, String email) throws HttpException {
    return getOwnVotesForPotm(
        limit,
        matchIds,
        startAfter,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        FANS_UNITED_CLIENTS,
        email,
        true,
        null);
  }
}
