package com.fansunited.automation.validators;

import static com.fansunited.automation.helpers.PostsGenerator.sortPostByPopular;
import static com.fansunited.automation.model.discussionapi.enums.PostSortType.LATEST;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.model.discussionapi.PostDto;
import com.fansunited.automation.model.discussionapi.enums.PostSortType;
import com.fansunited.automation.model.discussionapi.response.FilteredPostsResponse;
import io.restassured.response.Response;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;

public class PostValidator {

  public static void validateMetaTotalItems(Response response, int postCount) {
    response
        .then()
        .statusCode(HttpStatus.SC_OK)
        .body("meta.pagination.total_items", equalTo(postCount));
  }

  public static void validatePostsCount(Response response, int postsCount) {
    response.then().statusCode(HttpStatus.SC_OK);
    var posts = response.as(FilteredPostsResponse.class);
    Assertions.assertEquals(postsCount, posts.getData().size());
  }

  public static void validatePostsFilteredByDeleted(Response response) {
    response.then().statusCode(HttpStatus.SC_OK);
    var filteredPosts = response.as(FilteredPostsResponse.class);
    boolean isAllDeletedPosts = filteredPosts.getData().stream().allMatch(PostDto::isDeleted);
    Assertions.assertTrue(isAllDeletedPosts);
  }

  public static void validatePostsFilteredByModerated(Response response, int expectedPostsCount) {
    response.then().statusCode(HttpStatus.SC_OK);
    validatePostsCount(response, expectedPostsCount);
    var filteredPosts = response.as(FilteredPostsResponse.class);
    boolean isAllModeratedPosts = filteredPosts.getData().stream().allMatch(PostDto::isModerated);
    Assertions.assertTrue(isAllModeratedPosts, "All posts must be moderated");
  }

  public static void validatePostsFilteredByTypePrivate(Response response, int expectedPostsCount) {
    validatePostsCount(response, expectedPostsCount);
    validateAllPostsType(response, "PRIVATE");
  }

  public static void validatePostsFilteredByTypePublic(Response response, int expectedPostsCount) {
    validatePostsCount(response, expectedPostsCount);
    validateAllPostsType(response, "PUBLIC");
  }

  private static void validateAllPostsType(Response response, String type) {
    response.then().statusCode(HttpStatus.SC_OK);
    List<PostDto> postDts = response.getBody().jsonPath().getList("data", PostDto.class);
    boolean allPrivate;
    if (type.equals("PRIVATE")) {
      allPrivate = postDts.stream().allMatch(PostDto::isPrivatePost);
      Assertions.assertTrue(allPrivate, "All posts must be PRIVATE");
    } else {
      allPrivate = postDts.stream().allMatch(PostDto::isPrivatePost);
      Assertions.assertFalse(allPrivate, "All posts must be PUBLIC");
    }
  }

  public static void validatePostFilteredBySortReported(Response response) {
    response.then().statusCode(HttpStatus.SC_OK);
    validateSortedPostDtos(response, Comparator.comparingInt(PostDto::getReportsCount));
  }

  public static void validatePostFilteredBySortPopular(Response response) {
    response.then().statusCode(HttpStatus.SC_OK);
    List<PostDto> postsDtsResponse = response.getBody().jsonPath().getList("data", PostDto.class);
    Assertions.assertEquals(sortPostByPopular(postsDtsResponse), postsDtsResponse);
  }

  public static void validatePostFilteredBySortInteracted(Response response) {
    response.then().statusCode(HttpStatus.SC_OK);
    validateSortedPostDtos(response, Comparator.comparingInt(PostDto::getReactionsCount));
  }

  public static void validatePostsFilteredBySortLatest(
      Response response, PostSortType postSortType) {
    response.then().statusCode(HttpStatus.SC_OK);
    List<PostDto> postDtoList = response.getBody().jsonPath().getList("data", PostDto.class);
    Assertions.assertTrue(verifySortingByDate(postDtoList, postSortType));
  }

  private static void validateSortedPostDtos(
      Response response, Comparator<PostDto> postDtoComparator) {
    List<PostDto> responseBody = response.getBody().jsonPath().getList("data", PostDto.class);
    List<PostDto> copyPostDtoList = new ArrayList<>(responseBody);
    copyPostDtoList.sort(postDtoComparator.reversed());
    Assertions.assertEquals(responseBody, copyPostDtoList, "Posts are NOT sorted correctly!");
  }

  public static boolean verifySortingByDate(List<PostDto> posts, PostSortType sortType) {
    for (int i = 0; i < posts.size() - 1; i++) {
      LocalDateTime currentDateTime =
          LocalDateTime.parse(posts.get(i).getCreatedAt(), DateTimeFormatter.ISO_DATE_TIME);
      LocalDateTime nextDateTime =
          LocalDateTime.parse(posts.get(i + 1).getCreatedAt(), DateTimeFormatter.ISO_DATE_TIME);
      boolean isOrdered;
      if (sortType == LATEST) {
        isOrdered = nextDateTime.isBefore(currentDateTime);
      } else {
        isOrdered = nextDateTime.isAfter(currentDateTime);
      }
      if (!isOrdered) {
        return false;
      }
    }
    return true;
  }

  public static void validateAllPostsAreReported(Response response) {
    response.then().statusCode(HttpStatus.SC_OK);
    List<PostDto> responseBody = response.getBody().jsonPath().getList("data", PostDto.class);
    boolean allPostsHaveReports =
        !responseBody.isEmpty()
            && responseBody.stream().allMatch(post -> post.getReportsCount() > 0);

    Assertions.assertTrue(allPostsHaveReports, "All posts should have at least one report");
  }

  public static void validatePostsStartAfter(
      Response response, String removedPostId) {

    var responsePostDtos = response.as(FilteredPostsResponse.class).getData();

    Assertions.assertFalse(
        responsePostDtos.stream().anyMatch(postDto -> postDto.getId().equals(removedPostId)));
  }
}
