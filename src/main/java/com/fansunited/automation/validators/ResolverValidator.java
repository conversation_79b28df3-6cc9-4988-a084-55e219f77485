package com.fansunited.automation.validators;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.FIXTURES_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.POINTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_OUTCOME;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_RESETTLED_AT_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.RESULT_SETTLED_AT_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.SETTLED_FIXTURES_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static org.awaitility.Awaitility.await;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.nullValue;

import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.ResultStatus;
import com.fansunited.automation.model.predictionapi.predictions.AdvancedPredictions;
import com.fansunited.automation.model.predictionapi.predictions.PredictionsData;
import io.restassured.response.Response;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;

public class ResolverValidator {
  private ResolverValidator() {
  }

  public static void validateOnlyPredictionFixturesForFinishedMatchesInTopXGameAreResolved(
      Response ownPredictionsResponse,
      List<Match> resolvedMatchList, ResultOutcome resultOutcome) {

    var predictionsResponseDto = ownPredictionsResponse.as(PredictionsData.class);
    var resolvedFixtures = predictionsResponseDto.getData().get(0)
        .getFixtures()
        .stream()
        .filter(
            predictionFixture -> predictionFixture.getResult().getStatus() == ResultStatus.SETTLED)
        .toList();

    MatcherAssert.assertThat("Resolved fixtures list is empty", resolvedFixtures, not(empty()));

    MatcherAssert.assertThat("Top X prediction fixture list does NOT contain all resolved matches",
        resolvedFixtures.stream().map(PredictionFixture::getMatchId).toList(),
        containsInAnyOrder(resolvedMatchList.stream().map(Match::getId).toArray()));

    MatcherAssert.assertThat("There are prediction fixtures with incorrect outcome",
        resolvedFixtures.stream()
            .map(predictionFixture -> predictionFixture.getResult().getOutcome())
            .toList(),
        everyItem(equalTo(resultOutcome)));

    var nonResolvedFixturesOptional = predictionsResponseDto.getData()
        .get(0)
        .getFixtures()
        .stream()
        .filter(
            predictionFixture -> predictionFixture.getResult().getStatus() != ResultStatus.SETTLED)
        .findAny();
    if (nonResolvedFixturesOptional.isPresent()) {
      MatcherAssert.assertThat("Incorrect prediction status",
          predictionsResponseDto.getData().get(0).getStatus(),
          equalTo(PredictionStatus.ACTIVE));
    } else {
      MatcherAssert.assertThat("Incorrect prediction status",
          predictionsResponseDto.getData().get(0).getStatus(),
          equalTo(PredictionStatus.WON));
    }
  }

  public static void validateGamePredictionsAreResolvedToWon(Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(not(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.SETTLED.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.CORRECT.getValue())))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.WON.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()));
  }

  public static void validateGamePredictionsAreResolvedToPartiallyCorrect(
      Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(not(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.SETTLED.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.PARTIALLY_CORRECT.getValue())))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.PARTIALLY_WON.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()));
  }

  public static void validateMatchQuizGamePredictionsAreResolvedToLost(
      Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(not(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.SETTLED.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.INCORRECT.getValue())))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.LOST.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + POINTS_PROP,
            everyItem(equalTo(0)))
        .body("data[0]." + POINTS_PROP, equalTo(0));
  }

  public static void validateSinglePredictionIsResolved(Response ownPredictionsResponse) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            not(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            nullValue())
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + STATUS_PROP,
            equalTo(ResultStatus.SETTLED.name()))
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_OUTCOME,
            not(ResultOutcome.NOT_VERIFIED.getValue()))
        .body("data[0]." + STATUS_PROP, not(PredictionStatus.ACTIVE.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(1));
  }

  private static final int MAX_RETRIES = 10;
  private static final int WAIT_TIME_SECONDS = 10;

  public static void waitForPredictionToBeResolved(String userEmail)
      throws HttpException, InterruptedException {
    int retries = 0;

    while (retries < MAX_RETRIES) {
      Resolver.resolve();

      Response response = PredictionsEndpoint.getOwnPredictionsForUser(userEmail);
      response.prettyPrint();

      // Check if the prediction is no longer ACTIVE
      boolean isResolved = response
          .then()
          .extract()
          .body()
          .path("data[0]." + STATUS_PROP)
          .equals(PredictionStatus.ACTIVE.getValue()) == false;

      if (isResolved) {
        System.out.println("Prediction resolved.");
        validateSinglePredictionIsResolved(response);
        return;  // Exit if the prediction is resolved
      }

      // Prediction is still ACTIVE, wait before retrying
      retries++;
      if (retries < MAX_RETRIES) {
        System.out.println("Prediction still ACTIVE. Retrying in " + WAIT_TIME_SECONDS + " seconds...");
        try {
          Thread.sleep(WAIT_TIME_SECONDS * 1000);
        } catch (InterruptedException e) {
          throw new RuntimeException("Thread sleep interrupted", e);
        }
      }
    }

    // Timeout reached, continue with a warning
    System.out.println("Warning: Prediction status is still ACTIVE after maximum retries.");
  }

  public static void validateSinglePredictionIsResolvedToWon(Response ownPredictionsResponse) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            not(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            nullValue())
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + STATUS_PROP,
            equalTo(ResultStatus.SETTLED.name()))
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_OUTCOME,
            equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.WON.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(1));
  }

  public static void validateSinglePredictionIsResolvedToLost(Response ownPredictionsResponse) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            not(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            nullValue())
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + STATUS_PROP,
            equalTo(ResultStatus.SETTLED.name()))
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_OUTCOME,
            equalTo(ResultOutcome.INCORRECT.getValue()))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.LOST.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(1));
  }

  public static void validateMatchQuizGamePredictionsAreResolvedToPartiallyCorrect(
      Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(not(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.SETTLED.name())))
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[1]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.INCORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[2]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[3]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.INCORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[4]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[5]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[6]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[7]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[8]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[9]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[10]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[11]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.INCORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[12]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[13]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[14]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[15]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.INCORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[16]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[17]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[18]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[19]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[20]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[21]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[22]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[23]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[24]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[25]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[26]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[27]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.INCORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[28]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[29]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[30]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[31]." + RESULT_PROP + "." + RESULT_OUTCOME, equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.PARTIALLY_WON.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()))
        .body("data[0].fixtures[0].result.points", equalTo(10))
        .body("data[0].fixtures[1].result.points", equalTo(0))
        .body("data[0].fixtures[2].result.points", equalTo(10))
        .body("data[0].fixtures[3].result.points", equalTo(0))
        .body("data[0].fixtures[4].result.points", equalTo(10))
        .body("data[0].fixtures[5].result.points", equalTo(10))
        .body("data[0].fixtures[6].result.points", equalTo(10))
        .body("data[0].fixtures[7].result.points", equalTo(10))
        .body("data[0].fixtures[8].result.points", equalTo(10))
        .body("data[0].fixtures[9].result.points", equalTo(10))
        .body("data[0].fixtures[10].result.points", equalTo(15))
        .body("data[0].fixtures[11].result.points", equalTo(0))
        .body("data[0].fixtures[12].result.points", equalTo(15))
        .body("data[0].fixtures[13].result.points", equalTo(15))
        .body("data[0].fixtures[14].result.points", equalTo(15))
        .body("data[0].fixtures[15].result.points", equalTo(0))
        .body("data[0].fixtures[16].result.points", equalTo(15))
        .body("data[0].fixtures[17].result.points", equalTo(15))
        .body("data[0].fixtures[18].result.points", equalTo(5))
        .body("data[0].fixtures[19].result.points", equalTo(30))
        .body("data[0].fixtures[20].result.points", equalTo(10))
        .body("data[0].fixtures[21].result.points", equalTo(10))
        .body("data[0].fixtures[22].result.points", equalTo(40))
        .body("data[0].fixtures[23].result.points", equalTo(30))
        .body("data[0].fixtures[24].result.points", equalTo(20))
        .body("data[0].fixtures[25].result.points", equalTo(20))
        .body("data[0].fixtures[26].result.points", equalTo(50))
        .body("data[0].fixtures[27].result.points", equalTo(0))
        .body("data[0].fixtures[28].result.points", equalTo(30))
        .body("data[0].fixtures[29].result.points", equalTo(50))
        .body("data[0].fixtures[30].result.points", equalTo(50))
        .body("data[0].fixtures[31].result.points", equalTo(30))
        .body("data[0].points", equalTo(545));
  }

  public static void validateTopXGamePredictionsAreResolvedToWon(Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(not(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.SETTLED.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.CORRECT.getValue())))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.WON.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + POINTS_PROP,
            everyItem(equalTo(50)));
  }

  public static void validateTopXGamePredictionsAreResolvedToLost(Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(not(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.SETTLED.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.INCORRECT.getValue())))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.LOST.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + POINTS_PROP,
            everyItem(equalTo(0)))
        .body("data[0]." + POINTS_PROP, equalTo(0));
  }

  public static void validateMatchQuizGamePredictionsAreResolvedToWon(
      Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(not(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.SETTLED.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.CORRECT.getValue())))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.WON.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()))
        .body("data[0].fixtures[0].result.points", equalTo(10))
        .body("data[0].fixtures[1].result.points", equalTo(10))
        .body("data[0].fixtures[2].result.points", equalTo(10))
        .body("data[0].fixtures[3].result.points", equalTo(10))
        .body("data[0].fixtures[4].result.points", equalTo(10))
        .body("data[0].fixtures[5].result.points", equalTo(10))
        .body("data[0].fixtures[6].result.points", equalTo(10))
        .body("data[0].fixtures[7].result.points", equalTo(10))
        .body("data[0].fixtures[8].result.points", equalTo(10))
        .body("data[0].fixtures[9].result.points", equalTo(10))
        .body("data[0].fixtures[10].result.points", equalTo(15))
        .body("data[0].fixtures[11].result.points", equalTo(15))
        .body("data[0].fixtures[12].result.points", equalTo(15))
        .body("data[0].fixtures[13].result.points", equalTo(15))
        .body("data[0].fixtures[14].result.points", equalTo(15))
        .body("data[0].fixtures[15].result.points", equalTo(15))
        .body("data[0].fixtures[16].result.points", equalTo(15))
        .body("data[0].fixtures[17].result.points", equalTo(15))
        .body("data[0].fixtures[18].result.points", equalTo(5))
        .body("data[0].fixtures[19].result.points", equalTo(30))
        .body("data[0].fixtures[20].result.points", equalTo(10))
        .body("data[0].fixtures[21].result.points", equalTo(10))
        .body("data[0].fixtures[22].result.points", equalTo(40))
        .body("data[0].fixtures[23].result.points", equalTo(30))
        .body("data[0].fixtures[24].result.points", equalTo(20))
        .body("data[0].fixtures[25].result.points", equalTo(20))
        .body("data[0].fixtures[26].result.points", equalTo(50))
        .body("data[0].fixtures[27].result.points", equalTo(50))
        .body("data[0].fixtures[28].result.points", equalTo(30))
        .body("data[0].fixtures[29].result.points", equalTo(50))
        .body("data[0].fixtures[30].result.points", equalTo(50))
        .body("data[0].points", equalTo(645));
  }

  public static void validateTopXGameForAdvancedCorrectScore(
      AdvancedPredictions advancedPrediction, Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList) {

    var points = getCorrectAdvancedCorrectScorePoints(advancedPrediction);

    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(not(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.SETTLED.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(returnOutcomeAsPerAdvanceCorrectScorePrediction(advancedPrediction).getValue())))
        .body("data[0]." + STATUS_PROP,
            equalTo(returnStatusAsPerAdvanceCorrectScorePrediction(advancedPrediction).getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + POINTS_PROP, everyItem(equalTo(points)))
        .body("data[0]." + POINTS_PROP, equalTo(points *
            ownPredictionsResponse.then().extract().jsonPath().getList("data[0].fixtures").size()
        ));
  }

  public static void validateSinglePredictionForAdvancedCorrectScore(
      AdvancedPredictions advancedPrediction, Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList) {

    var points = getCorrectAdvancedCorrectScorePoints(advancedPrediction);

    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(not(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.SETTLED.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            equalTo(List.of(returnOutcomeAsPerAdvanceCorrectScorePrediction(advancedPrediction).getValue())))
        .body("data[0]." + STATUS_PROP,
            equalTo(returnStatusAsPerAdvanceCorrectScorePrediction(advancedPrediction).getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + POINTS_PROP, everyItem(equalTo(points)))
        .body("data[0]." + POINTS_PROP, equalTo(points *
            ownPredictionsResponse.then().extract().jsonPath().getList("data[0].fixtures").size()
        ));
  }

  public static void validateMatchQuizGameForAdvancedCorrectScore(
      AdvancedPredictions advancedPrediction, Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList) {

    var points = getCorrectAdvancedCorrectScorePoints(advancedPrediction);

    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(not(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.SETTLED.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            equalTo(List.of(returnOutcomeAsPerAdvanceCorrectScorePrediction(advancedPrediction).getValue())))
        .body("data[0]." + STATUS_PROP,
            equalTo(returnStatusAsPerAdvanceCorrectScorePrediction(advancedPrediction).getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()))
        .body("data[0].fixtures[0].result.points", equalTo(points))
        .body("data[0].points", equalTo(points));
  }

  private static ResultOutcome returnOutcomeAsPerAdvanceCorrectScorePrediction(
      AdvancedPredictions advancedPrediction) {
    switch (advancedPrediction) {
      case CORRECT_RESULT -> {
        return ResultOutcome.CORRECT;
      }
      case INCORRECT_RESULT -> {
        return ResultOutcome.INCORRECT;
      }
      case CORRECT_HOME_TEAM,
          CORRECT_HOME_TEAM_WITH_CORRECT_NEGATIVE_DIFF,
          CORRECT_AWAY_TEAM,
          CORRECT_AWAY_TEAM_WITH_CORRECT_NEGATIVE_DIFF,
          CORRECT_HOME_TEAM_AND_OUTCOME,
          CORRECT_AWAY_TEAM_AND_OUTCOME,
          CORRECT_DIFF_AND_OUTCOME -> {
        return ResultOutcome.PARTIALLY_CORRECT;
      }
      default ->
        throw new IllegalArgumentException(
            "Unrecognized Advanced Correct Score Prediction Outcome: " + advancedPrediction);

    }
  }

  private static PredictionStatus returnStatusAsPerAdvanceCorrectScorePrediction(
      AdvancedPredictions advancedPrediction) {
    switch (advancedPrediction) {
      case CORRECT_RESULT -> {
        return PredictionStatus.WON;
      }
      case INCORRECT_RESULT -> {
        return PredictionStatus.LOST;
      }
      case CORRECT_HOME_TEAM,
          CORRECT_HOME_TEAM_WITH_CORRECT_NEGATIVE_DIFF,
          CORRECT_AWAY_TEAM,
          CORRECT_AWAY_TEAM_WITH_CORRECT_NEGATIVE_DIFF,
          CORRECT_HOME_TEAM_AND_OUTCOME,
          CORRECT_AWAY_TEAM_AND_OUTCOME,
          CORRECT_DIFF_AND_OUTCOME -> {
        return PredictionStatus.PARTIALLY_WON;
      }
      default ->
        throw new IllegalArgumentException(
            "Unrecognized Advanced Correct Score Prediction Outcome: " + advancedPrediction);

    }
  }

  private static int getCorrectAdvancedCorrectScorePoints(AdvancedPredictions advancedPrediction) {
    switch (advancedPrediction) {
      case CORRECT_RESULT -> {
        return 50;
      }
      case INCORRECT_RESULT -> {
        return 0;
      }
      case CORRECT_HOME_TEAM,
          CORRECT_HOME_TEAM_WITH_CORRECT_NEGATIVE_DIFF,
          CORRECT_AWAY_TEAM,
          CORRECT_AWAY_TEAM_WITH_CORRECT_NEGATIVE_DIFF -> {
        return 5;
      }
      case CORRECT_HOME_TEAM_AND_OUTCOME,
          CORRECT_AWAY_TEAM_AND_OUTCOME,
          CORRECT_DIFF_AND_OUTCOME -> {
        return 10;
      }
      default ->
          throw new IllegalArgumentException(
              "Unrecognized Advanced Correct Score Prediction Outcome: " + advancedPrediction);
    }
  }

  public static void validateTopXGamePredictionsWithOnePostponedMatch(
      Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList, int fixtureIndexWithPostponedMatch) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "[1]." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            not(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "[" + fixtureIndexWithPostponedMatch + "]."
                + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            is(nullValue()))

        .body("data[0]." + FIXTURES_PROP + "[2]." + RESULT_PROP + "." + STATUS_PROP,
            equalTo(ResultStatus.SETTLED.name()))
        .body("data[0]." + FIXTURES_PROP + "[" + fixtureIndexWithPostponedMatch + "]."
                + RESULT_PROP + "." + STATUS_PROP,
            equalTo(ResultStatus.VOID.name()))
        .body("data[0]." + FIXTURES_PROP + "[3]." + RESULT_PROP + "." + RESULT_OUTCOME,
            equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[" + fixtureIndexWithPostponedMatch + "]."
                + RESULT_PROP + "." + RESULT_OUTCOME,
            equalTo(ResultOutcome.VOID.getValue()))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.WON.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()))
        .body(String.format("data[0].fixtures[%s].result.points", fixtureIndexWithPostponedMatch),
            equalTo(0))
        .body("data[0].fixtures[4].result.points", equalTo(50))
        .body("data[0].points", equalTo(250));
  }

  public static void validateTopXGamePredictionsWithOnePostponedMatchPartiallyCorrect(
      Response ownPredictionsResponse,
      List<PredictionFixture> fixtureList, int fixtureIndexWithPostponedMatch) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "[1]." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            not(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "[" + fixtureIndexWithPostponedMatch + "]."
                + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            is(nullValue()))

        .body("data[0]." + FIXTURES_PROP + "[2]." + RESULT_PROP + "." + STATUS_PROP,
            equalTo(ResultStatus.SETTLED.name()))
        .body("data[0]." + FIXTURES_PROP + "[" + fixtureIndexWithPostponedMatch + "]."
                + RESULT_PROP + "." + STATUS_PROP,
            equalTo(ResultStatus.VOID.name()))
        .body("data[0]." + FIXTURES_PROP + "[3]." + RESULT_PROP + "." + RESULT_OUTCOME,
            equalTo(ResultOutcome.CORRECT.getValue()))
        .body("data[0]." + FIXTURES_PROP + "[" + fixtureIndexWithPostponedMatch + "]."
                + RESULT_PROP + "." + RESULT_OUTCOME,
            equalTo(ResultOutcome.VOID.getValue()))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.PARTIALLY_WON.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(fixtureList.size()))
        .body(String.format("data[0].fixtures[%s].result.points", fixtureIndexWithPostponedMatch),
            equalTo(0))
        .body("data[0].fixtures[4].result.points", equalTo(50))
        .body("data[0].points", equalTo(210));
  }

  public static void validateTopXGamePredictionsWithAllMatchesPostponed(
      Response ownPredictionsResponse) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(is(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.VOID.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.VOID.getValue())))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.CANCELED.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(0))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + POINTS_PROP,
            everyItem(equalTo(0)))
        .body("data[0]." + POINTS_PROP, equalTo(0));
  }

  public static void validateCanceledTopXGameWithOnePredictionWon(
      Response ownPredictionsResponse) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.VOID.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.VOID.getValue())))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.CANCELED.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(1))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + POINTS_PROP,
            everyItem(equalTo(0)))
        .body("data[0]." + POINTS_PROP, equalTo(0));
  }

  public static void validateMatchQuizGamePredictionsWithMatchPostponed(
      Response ownPredictionsResponse) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            everyItem(is(nullValue())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            everyItem(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + STATUS_PROP,
            everyItem(equalTo(ResultStatus.VOID.name())))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + RESULT_OUTCOME,
            everyItem(equalTo(ResultOutcome.VOID.getValue())))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.CANCELED.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(0))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + POINTS_PROP,
            everyItem(equalTo(0)))
        .body("data[0].points", equalTo(0));
  }

  public static void validateSinglePredictionIsResolvedToVoid(Response ownPredictionsResponse) {
    ownPredictionsResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_SETTLED_AT_PROP,
            is(nullValue()))
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_RESETTLED_AT_PROP,
            nullValue())
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + STATUS_PROP,
            equalTo(ResultStatus.VOID.name()))
        .body("data[0]." + FIXTURES_PROP + "[0]." + RESULT_PROP + "." + RESULT_OUTCOME,
            equalTo(ResultOutcome.VOID.getValue()))
        .body("data[0]." + STATUS_PROP, equalTo(PredictionStatus.CANCELED.getValue()))
        .body("data[0]." + SETTLED_FIXTURES_PROP, equalTo(0))
        .body("data[0]." + FIXTURES_PROP + "." + RESULT_PROP + "." + POINTS_PROP,
            everyItem(equalTo(0)))
        .body("data[0].points", equalTo(0));
  }

  public static void validateSinglePredictionIsResolvedToExpectedStatus(Response ownPredictionsResponse, String status) {

    switch (status){
      case "WON":
        validateSinglePredictionIsResolvedToWon(ownPredictionsResponse);
        break;
      case "LOST":
        validateSinglePredictionIsResolvedToLost(ownPredictionsResponse);
        break;
      case "VOID":
        validateSinglePredictionIsResolvedToVoid(ownPredictionsResponse);
        break;
      default: throw new IllegalArgumentException(String.format("Status %s is invalid! Valid statuses are: [\"WON\", \"LOST\", \"VOID\"]", status));
    }
  }
}
