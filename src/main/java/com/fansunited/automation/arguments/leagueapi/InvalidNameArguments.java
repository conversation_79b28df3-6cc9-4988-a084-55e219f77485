package com.fansunited.automation.arguments.leagueapi;

import java.util.stream.Stream;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.ArgumentsProvider;

public class InvalidNameArguments implements ArgumentsProvider {
  private final String longName = RandomStringUtils.randomAlphabetic(101);

  @Override
  public Stream<? extends Arguments> provideArguments(ExtensionContext context) {
    return Stream.of(
            new InvalidNameHolder("", HttpStatus.SC_BAD_REQUEST),
            new InvalidNameHolder(longName, HttpStatus.SC_BAD_REQUEST),
            new InvalidNameHolder(null, HttpStatus.SC_BAD_REQUEST))
        .map(Arguments::of);
  }
}
