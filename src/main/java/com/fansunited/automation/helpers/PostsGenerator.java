package com.fansunited.automation.helpers;

import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.discussionapi.CreatePostEndpoint.createPostForDiscussion;
import static com.fansunited.automation.core.apis.discussionapi.DeletePostEndpoint.deleteDiscussionsPost;
import static com.fansunited.automation.core.apis.discussionapi.GetDiscussionPostsEndpoint.getDiscussionsPosts;
import static com.fansunited.automation.core.apis.discussionapi.ModeratePostEndpoint.moderatePost;
import static com.fansunited.automation.core.apis.discussionapi.ReactPostEndpoint.reactUsersPost;
import static com.fansunited.automation.core.apis.discussionapi.ReportPostEndpoint.reportPosResponse;
import static com.fansunited.automation.core.base.AuthBase.createUsers;
import static com.fansunited.automation.core.base.discusionapi.DiscussionApiBaseTest.createDiscussionForTests;
import static com.fansunited.automation.core.base.leaguesapi.LeagueBaseTest.createTemplateForLeague;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.Helper.convertLocalDateToIsoDate;
import static com.fansunited.automation.model.discussionapi.ReactionType.CARE;
import static com.fansunited.automation.model.discussionapi.ReactionType.LAUGH;
import static com.fansunited.automation.model.discussionapi.ReactionType.LIKE;
import static com.fansunited.automation.model.discussionapi.ReactionType.LOVE;
import static com.fansunited.automation.validators.LeaguesApiValidator.createPrivateLeagueRequest;
import static com.fansunited.automation.validators.LeaguesApiValidator.getUsersIds;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.leagueapi.leagues.CreateLeagueEndpoint;
import com.fansunited.automation.model.discussionapi.PostDto;
import com.fansunited.automation.model.discussionapi.PostReportReason;
import com.fansunited.automation.model.discussionapi.ReactionDto;
import com.fansunited.automation.model.discussionapi.ReactionType;
import com.fansunited.automation.model.discussionapi.request.CreatePostRequest;
import com.fansunited.automation.model.discussionapi.request.MadeReactionRequest;
import com.fansunited.automation.model.discussionapi.request.ModeratePostRequest;
import com.fansunited.automation.model.discussionapi.request.ReportPostRequest;
import com.fansunited.automation.model.leaguesapi.enums.LeagueType;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;

public class PostsGenerator {

  /**
   * Create a discussion and then add multiple public posts to it.
   *
   * @param count how many pasts
   * @param email user email
   * @return List of posts ids
   * @throws HttpException exception
   */
  public static List<String> createPublicPosts(int count, String email) throws HttpException {
    String discussionId = createDiscussionForTests(false).getData().getId();
    List<CreatePostRequest> createPostRequestList = new ArrayList<>();
    for (int i = 0; i < count; i++) {
      createPostRequestList.add(
          CreatePostRequest.builder().content(new Faker().internet().uuid()).build());
    }

    List<String> postsIds = new ArrayList<>();
    createPostRequestList.forEach(
        postRequest -> {
          try {
            postsIds.add(
                createPostForDiscussion(
                        postRequest,
                        discussionId,
                        CLIENT_AUTOMATION_ID,
                        FANS_UNITED_PROFILE,
                        email,
                        AuthConstants.ENDPOINTS_API_KEY,
                        ContentType.JSON,
                        null)
                    .getBody()
                    .jsonPath()
                    .get("data.id"));
            try {
              Thread.sleep(1000);
            } catch (InterruptedException e) {
              throw new RuntimeException(e);
            }

          } catch (HttpException e) {
            throw new RuntimeException(e);
          }
        });
    return postsIds;
  }

  /**
   * Delete multiple posts
   *
   * @param postsIds The posts that will be deleted
   * @param email user email
   */
  public static void deletePosts(List<String> postsIds, String email) {

    postsIds.forEach(
        id -> {
          try {
            deleteDiscussionsPost(
                id,
                CLIENT_AUTOMATION_ID,
                AuthConstants.ENDPOINTS_API_KEY,
                ContentType.JSON,
                FANS_UNITED_PROFILE,
                email,
                null);

          } catch (HttpException e) {
            throw new RuntimeException(e);
          }
        });
  }

  /**
   * Moderate multiple post
   *
   * @param postsIds All posts that will be moderated
   */
  public static void moderatePosts(List<String> postsIds) {
    var moderatePostRequest =
        ModeratePostRequest.builder().moderationReason(new Faker().lorem().sentence(2)).build();

    postsIds.forEach(
        id -> {
          try {
            moderatePost(
                moderatePostRequest,
                id,
                CLIENT_AUTOMATION_ID,
                AuthConstants.ENDPOINTS_API_KEY,
                ContentType.JSON,
                FANS_UNITED_CLIENTS,
                null,
                null);

          } catch (HttpException e) {
            throw new RuntimeException(e);
          }
        });
  }

  /**
   * Report a post by its postId. Set a random reason if not provided. If the reason is 'OTHER',
   * provide details in the reasonDetails field.
   *
   * @param postId The ID of the post to be reported.
   * @param reportReason If null will get random PostReasonType
   * @param user The user who is reporting the post.
   * @return Response
   * @throws HttpException exception
   */
  public static Response reportPost(String postId, PostReportReason reportReason, UserRecord user)
      throws HttpException {
    PostReportReason reason =
        reportReason != null ? reportReason : PostReportReason.getRandomReasonType();
    String reasonDetails = reason == PostReportReason.OTHER ? "Other reason" : null;

    var reportPostRequest =
        ReportPostRequest.builder().reason(reason).reasonDetails(reasonDetails).build();

    return reportPosResponse(
        reportPostRequest,
        postId,
        CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON,
        FANS_UNITED_PROFILE,
        user.getEmail(),
        null);
  }

  /**
   * Create multiple users that will report a post
   *
   * @param usersCount how many user will report the post
   * @param postId The ID of the post to be reported.
   * @param reason Type of reason it can be null
   * @throws IOException exception
   * @throws ExecutionException exception
   * @throws FirebaseAuthException exception
   * @throws InterruptedException exception
   */
  public static void multipleUsersReportPosts(
      String postId, PostReportReason reason, int usersCount)
      throws IOException, ExecutionException, FirebaseAuthException, InterruptedException {
    var profiles = createUsers(usersCount);
    profiles.forEach(
        profile -> {
          try {
            reportPost(postId, reason, profile);
          } catch (HttpException e) {
            throw new RuntimeException(e);
          }
        });
  }

  /**
   * Creates a private league and a private discussion.
   *
   * @param adminsCount how many members will participate in the league
   * @param userEmail User email who creates the league
   * @return League's id as String
   * @throws IOException exception
   * @throws ExecutionException exception
   * @throws FirebaseAuthException exception
   * @throws InterruptedException exception
   * @throws HttpException exception
   */
  public static String createPrivateLeague(int adminsCount, String userEmail)
      throws IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException,
          HttpException {
    var adminsIds = getUsersIds(createUsers(adminsCount));
    Faker faker = new Faker();
    var request =
        createPrivateLeagueRequest(
            faker.funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "),
            LeagueType.PRIVATE,
            createTemplateForLeague().getId(),
            faker.lorem().paragraph(1),
            null,
            adminsIds,
            null,
            null,
            null,
            faker.bool().bool(),
            convertLocalDateToIsoDate(LocalDate.now()));

    return CreateLeagueEndpoint.createLeague(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            AuthConstants.ENDPOINTS_API_KEY,
            CLIENT_AUTOMATION_ID,
            ContentType.JSON,
            null,
            request,
            userEmail)
        .getBody()
        .jsonPath()
        .get("data.id");
  }

  /**
   * Add post/s to private discussion
   *
   * @param discussionId the id of the private discussion
   * @param email user email
   * @param countPosts how many post you want to add
   * @throws HttpException exception
   */
  public static void addPostToPrivateDiscussion(String discussionId, String email, int countPosts)
      throws HttpException {
    var postRequest = CreatePostRequest.builder().content(new Faker().internet().uuid()).build();
    for (int i = 0; i < countPosts; i++) {
      postRequest.setContent(i + " content text");
      createPostForDiscussion(
          postRequest,
          discussionId,
          CLIENT_AUTOMATION_ID,
          FANS_UNITED_PROFILE,
          email,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          null);
    }
  }

  /**
   * User add a reaction. Set a random reaction if not provided.
   *
   * @param postId Post in which user will add reaction
   * @param reactionType Reaction type if null it will get random value
   * @param user The user who will react
   */
  public static void reactionOnPost(
      String postId, ReactionType reactionType, UserRecord user, boolean isPositive) {

    ReactionType reaction =
        reactionType != null ? reactionType : ReactionType.getRandomReactionType(isPositive);

    try {
      var reactionRequest = MadeReactionRequest.builder().reaction(reaction).build();
      reactUsersPost(
          reactionRequest,
          postId,
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          FANS_UNITED_PROFILE,
          user.getEmail(),
          null);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Multiple users add reactions on a single post
   *
   * @param postId User will react on this post
   * @param reaction User's reaction type
   * @param usersCount How many users will add reaction
   * @param isPositive If "true" will use only positive reactions (LOVE, LIKE, CARE, LAUGH)
   * @throws IOException exception
   * @throws ExecutionException exception
   * @throws FirebaseAuthException exception
   * @throws InterruptedException exception
   */
  public static void multipleUsersReactPosts(
      String postId, ReactionType reaction, int usersCount, boolean isPositive)
      throws IOException, ExecutionException, FirebaseAuthException, InterruptedException {
    var profiles = createUsers(usersCount);
    profiles.forEach(profile -> reactionOnPost(postId, reaction, profile, isPositive));
  }

  /**
   * This method sorts the given list of PostDto by the count of positive reactions and post ID in
   * descending order and returns sorted list.
   *
   * @param posts List<PostDto> to be sorted
   * @return Sorted List<PostDto>
   */
  public static List<PostDto> sortPostByPopular(List<PostDto> posts) {
    Map<PostDto, Integer> postDtoMap = new LinkedHashMap<>();

    // Calculating the positive reactions for each post
    for (PostDto post : posts) {
      List<ReactionDto> reactions = post.getReactions();
      int positiveReactionsCount = 0;

      for (ReactionDto reaction : reactions) {
        ReactionType type = reaction.getType();

        if (type.equals(LIKE) || type.equals(LOVE) || type.equals(LAUGH) || type.equals(CARE)) {
          positiveReactionsCount += reaction.getReactionCount();
        } else {
          postDtoMap.put(post, 0);
        }
      }
      if (positiveReactionsCount > 0) {
        postDtoMap.put(post, positiveReactionsCount);
      }
    }

    // Sorting the map by count of positive reactions and post id
    List<Map.Entry<PostDto, Integer>> entries = new ArrayList<>(postDtoMap.entrySet());
    entries.sort(
        (entry1, entry2) -> {
          int compareValues = entry2.getValue().compareTo(entry1.getValue());
          if (compareValues != 0) {
            return compareValues;
          }
          return entry2.getKey().getId().compareTo(entry1.getKey().getId());
        });

    List<PostDto> postsList = new LinkedList<>();
    for (Map.Entry<PostDto, Integer> entry : entries) {
      postsList.add(entry.getKey());
    }

    return postsList;
  }

  /**
   * This method sorts List of PostDto by reports count and post ID
   *
   * @param postDtos List of PostDto which will be sorted
   * @return Sorted list by reports_count and post id
   */
  public static List<PostDto> sortListByReportsCountAndName(List<PostDto> postDtos) {

    postDtos.sort(Comparator.comparing(PostDto::getReportsCount).thenComparing(PostDto::getId));
    return postDtos;
  }

  /**
   * This method search for particular discussion and returns its posts as List of post ids
   *
   * @param discussionId String
   * @return List<String> post ids
   * @throws HttpException exception
   */
  public static List<String> getDiscussionPostsIds(String discussionId) throws HttpException {

    var getDiscussionResponse =
        getDiscussionsPosts(
            discussionId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_CLIENTS,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            null);
    return getDiscussionResponse.getBody().jsonPath().getList("data.id");
  }
}
