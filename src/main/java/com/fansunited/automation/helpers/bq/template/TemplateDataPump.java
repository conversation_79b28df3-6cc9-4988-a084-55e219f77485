package com.fansunited.automation.helpers.bq.template;

import static com.fansunited.automation.mappers.EventMapper.TEMPLATE_TABLE;

import com.fansunited.automation.helpers.bq.BaseDataPump;
import com.fansunited.automation.mappers.EventMapper;
import com.google.cloud.bigquery.BigQuery;
import java.util.List;

public class TemplateDataPump extends BaseDataPump {
  private final BigQuery bigQuery;
  private final EventMapper eventMapper = new EventMapper();

  public TemplateDataPump(BigQuery bigQuery) {
    this.bigQuery = bigQuery;
  }

  public void insertSingleTemplate(List<String> profileIds) throws Exception {
    var templateItem = eventMapper.templateItem(profileIds, "FANTASY");
    insertJson(List.of(templateItem), bigQuery, TEMPLATE_TABLE);
  }
}
