votingApiBaseUrl=http://*************
votingApiPort=9030
miniGamesApiBaseUrl=http://*************
miniGamesApiPort=9040
leaguesApiBaseUrl=http://*************
leaguesApiPort=9010
discussionApiBaseUrl=http://*************
discussionApiPort=9020
profileApiBaseUrl=http://*************
profileApiPort=8010
predictionApiBaseUrl=http://*************
predictionApiPort=8040
footballApiBaseUrl=http://*************
footballApiPort=8080
reportingApiBaseUrl=http://*************
reportingApiPort=8020
resolverApiBaseUrl=http://*************
resolverApiPort=8050
loyaltyApiBaseUrl=http://*************
loyaltyApiPort=8060
mockApiBaseUrl=http://*************
mockApiPort=8090
clientApiBaseUrl=http://*************
clientApiPort=8070
firebaseWebApiKeyProfileProject=none
firebaseWebApiKeyClientsProject=none
firebasePathToServiceAccountProfileProject=/src/main/resources/FansUnitedAutomationProjectServiceAccountKey.json
googleEndpointsApiKey=local_automation_api_key
mysqlConnectionUrl=****************************************
mysqlUser=root
mysqlPass=1234
identityToolkitProfileUrl=http://*************:19199/identitytoolkit.googleapis.com/v1/accounts:signInWithPassword
identityToolkitClientUrl=http://*************:39199/identitytoolkit.googleapis.com/v1/accounts:signInWithPassword
useFirebaseEmulator=true
authUserPlatformOperator=<EMAIL>
authUserClientAdmin=<EMAIL>
authUserBillingManager=<EMAIL>
baseAuthUser=ringierwebhookuser
baseAuthPass=ringierPass
